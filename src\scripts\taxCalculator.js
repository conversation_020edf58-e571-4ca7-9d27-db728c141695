/**
 * 个税计算工具类
 * 提供个人所得税计算、申报表生成等功能
 */

/**
 * 个税税率表（综合所得年度税率表）
 */
export const TAX_BRACKETS = [
  { min: 0, max: 36000, rate: 0.03, deduction: 0 },
  { min: 36000, max: 144000, rate: 0.10, deduction: 2520 },
  { min: 144000, max: 300000, rate: 0.20, deduction: 16920 },
  { min: 300000, max: 420000, rate: 0.25, deduction: 31920 },
  { min: 420000, max: 660000, rate: 0.30, deduction: 52920 },
  { min: 660000, max: 960000, rate: 0.35, deduction: 85920 },
  { min: 960000, max: Infinity, rate: 0.45, deduction: 181920 }
]

/**
 * 基本扣除标准
 */
export const BASIC_DEDUCTION = 60000 // 年度基本扣除额

/**
 * 个税计算器类
 */
export class TaxCalculator {
  constructor() {
    this.taxBrackets = TAX_BRACKETS
    this.basicDeduction = BASIC_DEDUCTION
  }

  /**
   * 计算个人所得税
   * @param {Object} taxData - 税务数据
   * @returns {Object} 计算结果
   */
  calculateTax(taxData) {
    const {
      income = 0,                    // 本期收入
      socialInsurance = 0,           // 社保费用
      housingFund = 0,              // 住房公积金
      specialDeductions = 0,         // 专项附加扣除
      otherDeductions = 0,          // 其他扣除
      previousTax = 0,              // 已缴税额
      accumulatedIncome = 0,        // 累计收入
      accumulatedDeductions = 0     // 累计扣除
    } = taxData

    // 计算应纳税所得额
    const totalDeductions = socialInsurance + housingFund + specialDeductions + otherDeductions
    const taxableIncome = Math.max(0, income - totalDeductions - this.basicDeduction / 12)
    
    // 计算累计应纳税所得额
    const accumulatedTaxableIncome = Math.max(0, accumulatedIncome - accumulatedDeductions - this.basicDeduction)
    
    // 根据累计应纳税所得额计算应纳税额
    const accumulatedTax = this.calculateTaxByBrackets(accumulatedTaxableIncome)
    
    // 本期应纳税额 = 累计应纳税额 - 已缴税额
    const currentTax = Math.max(0, accumulatedTax - previousTax)
    
    // 确定税率档次
    const taxBracket = this.getTaxBracket(accumulatedTaxableIncome)
    
    return {
      income,                        // 本期收入
      totalDeductions,              // 总扣除额
      taxableIncome,                // 本期应纳税所得额
      accumulatedTaxableIncome,     // 累计应纳税所得额
      accumulatedTax,               // 累计应纳税额
      currentTax,                   // 本期应纳税额
      taxRate: taxBracket.rate,     // 适用税率
      effectiveRate: accumulatedIncome > 0 ? (accumulatedTax / accumulatedIncome) : 0, // 实际税率
      afterTaxIncome: income - currentTax, // 税后收入
      taxBracket: `${(taxBracket.rate * 100).toFixed(0)}%` // 税率档次
    }
  }

  /**
   * 根据税率表计算税额
   * @param {number} taxableIncome - 应纳税所得额
   * @returns {number} 应纳税额
   */
  calculateTaxByBrackets(taxableIncome) {
    if (taxableIncome <= 0) return 0

    for (const bracket of this.taxBrackets) {
      if (taxableIncome > bracket.min && taxableIncome <= bracket.max) {
        return taxableIncome * bracket.rate - bracket.deduction
      }
    }
    
    // 如果超出最高档次，使用最高税率
    const highestBracket = this.taxBrackets[this.taxBrackets.length - 1]
    return taxableIncome * highestBracket.rate - highestBracket.deduction
  }

  /**
   * 获取适用的税率档次
   * @param {number} taxableIncome - 应纳税所得额
   * @returns {Object} 税率档次信息
   */
  getTaxBracket(taxableIncome) {
    if (taxableIncome <= 0) return this.taxBrackets[0]

    for (const bracket of this.taxBrackets) {
      if (taxableIncome > bracket.min && taxableIncome <= bracket.max) {
        return bracket
      }
    }
    
    return this.taxBrackets[this.taxBrackets.length - 1]
  }

  /**
   * 批量计算个税
   * @param {Array} employeeData - 员工数据数组
   * @returns {Array} 计算结果数组
   */
  batchCalculate(employeeData) {
    return employeeData.map(employee => {
      const result = this.calculateTax(employee)
      return {
        ...employee,
        ...result
      }
    })
  }

  /**
   * 生成个税申报表数据
   * @param {Array} calculatedData - 计算后的数据
   * @returns {Array} 申报表数据
   */
  generateDeclarationData(calculatedData) {
    const headers = [
      '工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
      '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
      '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
      '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
      '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
      '准予扣除的捐赠额', '减免税额', '本期应纳税额', '备注'
    ]

    const dataRows = calculatedData.map(item => [
      item.employeeId || '',
      item.name || '',
      item.idType || '身份证',
      item.idNumber || '',
      item.income || 0,
      item.taxFreeIncome || 0,
      item.pensionInsurance || 0,
      item.medicalInsurance || 0,
      item.unemploymentInsurance || 0,
      item.housingFund || 0,
      item.childEducation || 0,
      item.continuingEducation || 0,
      item.housingLoanInterest || 0,
      item.housingRent || 0,
      item.elderCare || 0,
      item.infantCare || 0,
      item.personalPension || 0,
      item.enterpriseAnnuity || 0,
      item.commercialHealthInsurance || 0,
      item.taxDeferredPension || 0,
      item.otherDeductions || 0,
      item.charitableDonation || 0,
      item.taxReduction || 0,
      item.currentTax || 0,
      item.remarks || ''
    ])

    return [headers, ...dataRows]
  }

  /**
   * 生成算税底稿数据
   * @param {Array} calculatedData - 计算后的数据
   * @returns {Array} 算税底稿数据
   */
  generateCalculationData(calculatedData) {
    const headers = [
      '月份', '缴纳地点', '身份证号', '姓名', '成本所属项目', '薪酬类别',
      '本期收入', '基本养老保险费', '住房公积金', '基本医疗保险费',
      '失业保险费', '企业(职业)年金', '其它扣款', '调整收入', '调整扣除',
      '调整累计个税', '累计社保', '累计专项附加', '累计法定扣除',
      '累计调整扣除', '累计收入', '累计扣除', '累计应扣税款',
      '累计上次税款', '本次税款', '本次达到税率', '一次性年终奖校验'
    ]

    const dataRows = calculatedData.map(item => [
      item.month || new Date().toISOString().slice(0, 7),
      item.taxLocation || '北京市',
      item.idNumber || '',
      item.name || '',
      item.project || '',
      item.salaryType || '工资',
      item.income || 0,
      item.pensionInsurance || 0,
      item.housingFund || 0,
      item.medicalInsurance || 0,
      item.unemploymentInsurance || 0,
      item.enterpriseAnnuity || 0,
      item.otherDeductions || 0,
      item.adjustedIncome || 0,
      item.adjustedDeductions || 0,
      item.adjustedAccumulatedTax || 0,
      item.accumulatedSocialInsurance || 0,
      item.accumulatedSpecialDeductions || 0,
      item.accumulatedBasicDeductions || this.basicDeduction,
      item.accumulatedAdjustedDeductions || 0,
      item.accumulatedIncome || 0,
      item.accumulatedDeductions || 0,
      item.accumulatedTax || 0,
      item.previousTax || 0,
      item.currentTax || 0,
      item.taxBracket || '3%',
      item.bonusCheck || '否'
    ])

    return [headers, ...dataRows]
  }

  /**
   * 验证员工数据格式
   * @param {Array} data - 员工数据
   * @returns {Object} 验证结果
   */
  validateEmployeeData(data) {
    const errors = []
    const warnings = []

    if (!data || data.length === 0) {
      errors.push('数据不能为空')
      return { isValid: false, errors, warnings }
    }

    data.forEach((employee, index) => {
      const rowNum = index + 1

      // 检查必填字段
      if (!employee.name) {
        errors.push(`第${rowNum}行：姓名不能为空`)
      }
      
      if (!employee.idNumber) {
        errors.push(`第${rowNum}行：身份证号不能为空`)
      }

      // 检查数值字段
      const numericFields = ['income', 'socialInsurance', 'housingFund', 'specialDeductions']
      numericFields.forEach(field => {
        if (employee[field] && isNaN(employee[field])) {
          warnings.push(`第${rowNum}行：${field}应为数字`)
        }
      })

      // 检查身份证号格式
      if (employee.idNumber && !/^\d{17}[\dX]$/.test(employee.idNumber)) {
        warnings.push(`第${rowNum}行：身份证号格式可能不正确`)
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

/**
 * 创建个税计算器实例
 * @returns {TaxCalculator} 个税计算器实例
 */
export const createTaxCalculator = () => {
  return new TaxCalculator()
}

/**
 * 快速计算个税
 * @param {number} income - 月收入
 * @param {number} deductions - 总扣除额
 * @returns {Object} 计算结果
 */
export const quickCalculateTax = (income, deductions = 0) => {
  const calculator = new TaxCalculator()
  return calculator.calculateTax({
    income,
    socialInsurance: deductions * 0.3, // 假设社保占扣除额30%
    housingFund: deductions * 0.2,     // 假设公积金占扣除额20%
    specialDeductions: deductions * 0.5 // 假设专项附加扣除占50%
  })
}
