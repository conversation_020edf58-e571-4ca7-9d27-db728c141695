<template>
  <div class="tab-switch-test">
    <h2>Tab 切换数据保持测试</h2>
    
    <!-- Tab 导航 -->
    <div class="tabs">
      <button 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab', { active: activeTab === index }]"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </button>
    </div>
    
    <!-- 表格容器 -->
    <div class="table-container">
      <!-- 使用 v-show 替代 v-if -->
      <VTableComponent
        v-show="activeTab === 0"
        ref="table1"
        :key="'tab-0'"
        :data="table1Data"
        :width="800"
        :height="400"
        :editable="true"
        :enable-copy-paste="true"
        @data-change="(newData) => handleDataChange(newData, 0)"
      />
      
      <VTableComponent
        v-show="activeTab === 1"
        ref="table2"
        :key="'tab-1'"
        :data="table2Data"
        :width="800"
        :height="400"
        :editable="true"
        :enable-copy-paste="true"
        @data-change="(newData) => handleDataChange(newData, 1)"
      />
      
      <VTableComponent
        v-show="activeTab === 2"
        ref="table3"
        :key="'tab-2'"
        :data="table3Data"
        :width="800"
        :height="400"
        :editable="true"
        :enable-copy-paste="true"
        @data-change="(newData) => handleDataChange(newData, 2)"
      />
    </div>
    
    <!-- 测试按钮 -->
    <div class="test-buttons">
      <button @click="testDirectDataModification" class="test-btn">
        测试直接修改数据 (.value)
      </button>
      <button @click="refreshCurrentTable" class="test-btn">
        刷新当前表格
      </button>
      <button @click="refreshAllTables" class="test-btn">
        刷新所有表格
      </button>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info">
      <h3>调试信息</h3>
      <p>当前激活 Tab: {{ activeTab }}</p>
      <p>数据变更次数: {{ changeCount }}</p>
      <p>直接修改次数: {{ directModifyCount }}</p>
      <details>
        <summary>表格1数据</summary>
        <pre>{{ JSON.stringify(table1Data, null, 2) }}</pre>
      </details>
      <details>
        <summary>表格2数据</summary>
        <pre>{{ JSON.stringify(table2Data, null, 2) }}</pre>
      </details>
      <details>
        <summary>表格3数据</summary>
        <pre>{{ JSON.stringify(table3Data, null, 2) }}</pre>
      </details>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import VTableComponent from '@/components/VTableComponent.vue';

// Tab 定义
const tabs = [
  { name: '表格1' },
  { name: '表格2' },
  { name: '表格3' }
];

const activeTab = ref(0);
const changeCount = ref(0);
const directModifyCount = ref(0);

// 表格数据
const table1Data = ref([
  ['姓名', '年龄', '部门'],
  ['张三', 25, '研发部'],
  ['李四', 30, '销售部']
]);

const table2Data = ref([
  ['产品', '价格', '库存'],
  ['iPhone', 5999, 100],
  ['iPad', 3999, 50]
]);

const table3Data = ref([
  ['项目', '状态', '进度'],
  ['项目A', '进行中', '80%'],
  ['项目B', '已完成', '100%']
]);

// 表格引用
const table1 = ref(null);
const table2 = ref(null);
const table3 = ref(null);

// 数据缓存
const dataCache = ref({
  0: null,
  1: null,
  2: null
});

// 获取表格数据
const getTableData = (index) => {
  switch (index) {
    case 0: return table1Data;
    case 1: return table2Data;
    case 2: return table3Data;
    default: return null;
  }
};

// 获取表格引用
const getTableRef = (index) => {
  switch (index) {
    case 0: return table1.value;
    case 1: return table2.value;
    case 2: return table3.value;
    default: return null;
  }
};

// 处理数据变更
const handleDataChange = (newData, tabIndex) => {
  const tableData = getTableData(tabIndex);
  if (tableData) {
    tableData.value = newData;
    dataCache.value[tabIndex] = JSON.parse(JSON.stringify(newData));
    changeCount.value++;
    console.log(`Tab ${tabIndex} data changed:`, newData);
  }
};

// 刷新指定表格的数据显示
const refreshTableData = (tabIndex) => {
  const tableRef = getTableRef(tabIndex);
  const tableData = getTableData(tabIndex);

  if (tableRef && tableData && tableRef.setData) {
    tableRef.setData(tableData.value);
    console.log(`Refreshed table data for tab ${tabIndex}`);
  }
};

// 刷新所有表格
const refreshAllTables = () => {
  for (let i = 0; i <= 2; i++) {
    refreshTableData(i);
  }
};

// 刷新当前表格
const refreshCurrentTable = () => {
  refreshTableData(activeTab.value);
};

// 测试直接修改数据
const testDirectDataModification = () => {
  const tableData = getTableData(activeTab.value);
  if (tableData && tableData.value.length > 1) {
    // 直接修改第一行数据的第一个字段
    const timestamp = new Date().toLocaleTimeString();
    tableData.value[1][0] = `修改${directModifyCount.value + 1}_${timestamp}`;
    directModifyCount.value++;

    // 手动刷新表格显示
    refreshTableData(activeTab.value);

    console.log(`Direct modification ${directModifyCount.value} applied to tab ${activeTab.value}`);
  }
};

// 切换 Tab
const switchTab = (index) => {
  activeTab.value = index;

  // 恢复缓存数据
  if (dataCache.value[index]) {
    const tableData = getTableData(index);
    if (tableData) {
      tableData.value = JSON.parse(JSON.stringify(dataCache.value[index]));
      console.log(`Restored cached data for tab ${index}`);
    }
  }

  // 刷新表格尺寸
  nextTick(() => {
    const tableRef = getTableRef(index);
    if (tableRef && tableRef.getTableInstance) {
      const tableInstance = tableRef.getTableInstance();
      if (tableInstance) {
        tableInstance.resize();
        tableInstance.updateScrollBar();
      }
    }
  });
};
</script>

<style scoped>
.tab-switch-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #dee2e6;
}

.test-btn {
  padding: 8px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.test-btn:hover {
  background: #218838;
}

.test-btn:active {
  background: #1e7e34;
}

.tab {
  padding: 10px 20px;
  border: 1px solid #ddd;
  background: #f5f5f5;
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.tab:hover {
  background: #e0e0e0;
}

.tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.table-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  position: relative;
  height: 450px;
}

.debug-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #dee2e6;
}

.debug-info h3 {
  margin-top: 0;
}

.debug-info details {
  margin: 10px 0;
}

.debug-info pre {
  background: white;
  padding: 10px;
  border-radius: 3px;
  border: 1px solid #ddd;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

/* 确保隐藏的表格不占用空间 */
.table-container > div[style*="display: none"] {
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
  visibility: hidden !important;
}
</style>
