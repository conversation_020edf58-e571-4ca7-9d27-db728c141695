# VTable 增强功能说明

## 概述

本次更新为 VTable 组件增加了以下核心功能：
- 🗑️ **删除行功能**：支持选中多行批量删除
- ➕ **添加行功能**：快速添加新的数据行
- 📊 **Excel导出功能**：使用 ExcelJS 生成格式化的 Excel 文件
- 📥 **Excel导入功能**：读取 Excel 文件并追加到现有数据
- ↩️ **撤销功能**：支持撤销最近的操作，最多保存10步历史记录
- 📋 **模板下载**：提供标准的 Excel 导入模板

## 功能详细说明

### 1. 删除行功能

**使用方法：**
1. 在表格中选中要删除的行（可多选）
2. 点击工具栏中的 "🗑️ 删除行" 按钮
3. 确认删除操作

**特性：**
- 支持单行或多行批量删除
- 删除前会弹出确认对话框
- 自动更新表格显示
- 触发数据变化事件

**代码示例：**
```javascript
// 程序化删除行
vtableRef.value.deleteRows()

// 删除指定索引的行
vtableRef.value.deleteRowByIndex(2)
```

### 2. 添加行功能

**使用方法：**
1. 点击工具栏中的 "➕ 添加行" 按钮
2. 新行会自动添加到表格末尾
3. 自动滚动到新行并选中第一个单元格

**特性：**
- 新行所有字段初始化为空字符串
- 自动滚动到新添加的行
- 支持连续添加多行
- 触发数据变化事件

**代码示例：**
```javascript
// 添加空行
vtableRef.value.addRow()

// 添加带数据的行
vtableRef.value.addRowData(['E005', '新员工', '身份证', '...'])
```

### 3. Excel导出功能

**使用方法：**
1. 点击工具栏中的 "📊 导出Excel" 按钮
2. 系统自动生成并下载 Excel 文件

**特性：**
- 使用 ExcelJS 生成专业格式的 Excel 文件
- 自动设置表头样式（蓝色背景，白色字体，加粗）
- 自动调整列宽
- 数字类型自动格式化
- 添加边框和对齐方式
- 文件名包含当前日期

**代码示例：**
```javascript
// 导出当前表格为Excel
vtableRef.value.exportExcel()

// 导出为CSV（原有功能）
vtableRef.value.exportCSV()
```

### 4. Excel导入功能

**使用方法：**
1. 点击 "导入专项附加扣除表" 按钮
2. 选择要导入的 Excel 文件
3. 系统自动验证格式并追加数据

**特性：**
- 自动读取 Excel 文件第一个工作表
- 验证列结构是否与现有表格匹配
- 跳过标题行，只导入数据行
- 追加到现有数据末尾（不覆盖）
- 支持撤销导入操作
- 详细的错误提示

**支持的文件格式：**
- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

### 5. 撤销功能

**使用方法：**
1. 执行任何会修改数据的操作（导入、删除、添加等）
2. 点击 "↩️ 撤销操作" 按钮恢复到上一个状态

**特性：**
- 最多保存10步操作历史
- 保存完整的数据状态快照
- 包括当前活动的标签页状态
- 按钮显示当前可撤销步数
- 超出限制时自动清理最早的记录

**代码示例：**
```javascript
// 手动保存当前状态
saveToUndoStack()

// 撤销上一次操作
undoLastAction()
```

### 6. 模板下载功能

**使用方法：**
1. 点击 "📋 下载导入模板" 按钮
2. 系统生成标准模板并自动下载

**特性：**
- 包含完整的列结构
- 提供示例数据行
- 专业的格式设置
- 自动调整列宽
- 文件名包含当前日期

## 技术实现

### 依赖库
- `exceljs`: Excel 文件读写
- `@visactor/vtable`: 表格核心功能
- `@visactor/vtable-editors`: 表格编辑器

### 核心方法

#### VTableComponent 新增方法
```javascript
// 行操作
addRow()                    // 添加空行
deleteRows()               // 删除选中行
addRowData(rowData)        // 添加带数据的行
deleteRowByIndex(index)    // 删除指定行

// 选择操作
getSelectedRows()          // 获取选中的行索引

// 导出操作
exportExcel()              // 导出Excel文件
exportCSV()                // 导出CSV文件（原有）
```

#### SalaryTaxView 新增方法
```javascript
// 导入相关
importSpecialDeduction()           // 导入专项附加扣除
readExcelFile(file)               // 读取Excel文件
validateSpecialDeductionFormat()   // 验证数据格式
appendToSpecialDeduction()        // 追加数据

// 撤销相关
saveToUndoStack()                 // 保存状态
undoLastAction()                  // 撤销操作

// 模板相关
createSpecialDeductionTemplate()  // 创建下载模板
```

## 使用示例

### 完整的导入流程
```javascript
// 1. 下载模板
await createSpecialDeductionTemplate()

// 2. 用户填写模板后导入
const importSpecialDeduction = () => {
  // 创建文件选择器
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  
  input.onchange = async (event) => {
    const file = event.target.files[0]
    if (!file) return
    
    try {
      // 保存当前状态（支持撤销）
      saveToUndoStack()
      
      // 读取并验证Excel文件
      const data = await readExcelFile(file)
      if (validateSpecialDeductionFormat(data)) {
        // 追加数据
        appendToSpecialDeduction(data)
        alert('导入成功')
      } else {
        alert('格式不正确')
      }
    } catch (error) {
      alert('导入失败: ' + error.message)
    }
  }
  
  input.click()
}
```

### 表格操作示例
```javascript
// 获取表格组件引用
const vtableRef = ref(null)

// 添加新行
const addNewEmployee = () => {
  const newData = ['E006', '新员工', '身份证', '110101199001011234', ...]
  vtableRef.value.addRowData(newData)
}

// 删除选中行
const deleteSelected = () => {
  const selectedRows = vtableRef.value.getSelectedRows()
  if (selectedRows.length > 0) {
    vtableRef.value.deleteRows()
  } else {
    alert('请先选择要删除的行')
  }
}

// 导出数据
const exportData = () => {
  vtableRef.value.exportExcel()
}
```

## 注意事项

1. **Excel导入格式要求**：
   - 必须与现有表格列结构完全匹配
   - 第一行应为标题行（会被跳过）
   - 空行会被自动过滤

2. **撤销功能限制**：
   - 最多保存10步操作历史
   - 只能撤销数据修改操作
   - 不能撤销导出等只读操作

3. **性能考虑**：
   - 大量数据导入时可能需要时间
   - 撤销栈会占用内存，建议定期清理
   - Excel文件大小建议控制在10MB以内

4. **浏览器兼容性**：
   - 需要支持 FileReader API
   - 需要支持 Blob 和 URL.createObjectURL
   - 建议使用现代浏览器（Chrome 60+, Firefox 55+, Safari 11+）

## 演示页面

可以访问 `src/examples/SpecialDeductionImportDemo.vue` 查看完整的功能演示。
