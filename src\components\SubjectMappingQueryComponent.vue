<template>
  <div class="subject-mapping-query-component">
    <!-- 标题栏和返回按钮 -->
    <div class="header">
      <el-button 
        type="primary" 
        :icon="ArrowLeft" 
        @click="handleBack"
        class="back-button"
      >
        返回
      </el-button>
      <h1>科目对照</h1>
    </div>

    <!-- 查询面板 -->
    <div class="query-panel">
      <el-form 
        :model="queryForm" 
        :inline="true" 
        class="query-form"
        label-width="120px"
      >
        <!-- 查询字段 -->
        <el-form-item label="总账科目长文本">
          <el-input 
            v-model="queryForm.accountLongText" 
            placeholder="请输入总账科目长文本"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="科目分类1">
          <el-input 
            v-model="queryForm.subjectCategory1" 
            placeholder="请输入科目分类1"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        
        <el-form-item label="科目分类2">
          <el-input 
            v-model="queryForm.subjectCategory2" 
            placeholder="请输入科目分类2"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        
        <el-form-item label="科目方向">
          <el-select
            v-model="queryForm.subjectDirection"
            placeholder="请选择科目方向"
            clearable
            style="width: 150px"
          >
            <el-option label="借方" value="借方" />
            <el-option label="贷方" value="贷方" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :icon="Search" 
            @click="handleQuery"
            :loading="loading"
          >
            查询
          </el-button>
          <el-button 
            :icon="Refresh" 
            @click="handleReset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 功能操作面板 -->
    <div class="action-panel">
      <div class="action-left">
        <span class="result-count">
          共找到 {{ tableData.length > 0 ? tableData.length - 1 : 0 }} 条记录
        </span>
      </div>
      <div class="action-right">
        <span class="info-text">Excel导入、推送更新等功能已集成到表格中</span>
      </div>
    </div>

    <!-- 查询结果表格 -->
    <div class="result-panel">
      <VTableComponent
        v-if="tableData.length > 0"
        :data="tableData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :enable-push-update="true"
        :push-update-endpoint="updateEndpoint"
        :table-name="'科目对照'"
        class="query-table"
        @data-change="handleDataChange"
      />
      
      <div v-else-if="!loading && hasQueried" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      
      <div v-else-if="!hasQueried" class="no-query">
        <el-empty description="请输入查询条件进行查询" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Search, Refresh } from '@element-plus/icons-vue'
import VTableComponent from './VTableComponent.vue'
import axios from 'axios'

// 定义事件
const emit = defineEmits(['back'])

// 响应式数据
const queryForm = ref({
  accountLongText: '',
  subjectCategory1: '',
  subjectCategory2: '',
  subjectDirection: ''
})

const loading = ref(false)
const hasQueried = ref(false)
const tableData = ref([])
const tableWidth = ref(1000)
const tableHeight = ref(500)

// API端点
const apiEndpoint = '/api/query/subject-mapping'
const updateEndpoint = 'http://127.0.0.1:8000/api/update/subject-mapping'

// 模拟数据
const mockData = [
  ['总账科目长文本', '科目分类1', '科目分类2', '科目方向', '科目编码', '创建日期', '更新日期'],
  ['银行存款-工商银行', '资产类', '流动资产', '借方', '1001001', '2024-01-15', '2024-01-15'],
  ['银行存款-建设银行', '资产类', '流动资产', '借方', '1001002', '2024-01-15', '2024-01-15'],
  ['应收账款-客户A', '资产类', '流动资产', '借方', '1131001', '2024-01-16', '2024-01-16'],
  ['应收账款-客户B', '资产类', '流动资产', '借方', '1131002', '2024-01-16', '2024-01-16'],
  ['固定资产-办公设备', '资产类', '非流动资产', '借方', '1601001', '2024-01-17', '2024-01-17'],
  ['应付账款-供应商A', '负债类', '流动负债', '贷方', '2202001', '2024-01-18', '2024-01-18'],
  ['应付账款-供应商B', '负债类', '流动负债', '贷方', '2202002', '2024-01-18', '2024-01-18'],
  ['短期借款-银行贷款', '负债类', '流动负债', '贷方', '2001001', '2024-01-19', '2024-01-19'],
  ['实收资本-股东投资', '所有者权益类', '实收资本', '贷方', '4001001', '2024-01-20', '2024-01-20'],
  ['主营业务收入-产品销售', '损益类', '收入类', '贷方', '6001001', '2024-01-21', '2024-01-21']
]

// 处理返回
function handleBack() {
  emit('back')
}

// 处理查询
async function handleQuery() {
  loading.value = true
  hasQueried.value = true

  try {
    // 构造查询参数
    const filters = {
      '总账科目长文本': queryForm.value.accountLongText,
      '科目分类1': queryForm.value.subjectCategory1,
      '科目分类2': queryForm.value.subjectCategory2,
      '科目方向': queryForm.value.subjectDirection
    }

    // 尝试API调用
    const response = await axios.post(`http://localhost:8000${apiEndpoint}`, {
      filters: filters,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    })

    if (response.data && response.data.code === 200) {
      tableData.value = response.data.data || []
      ElMessage.success(`查询完成，共找到 ${tableData.value.length > 0 ? tableData.value.length - 1 : 0} 条记录`)
    } else {
      throw new Error(response.data?.message || '查询失败')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.warning('API调用失败，使用模拟数据')
    
    // 使用模拟数据进行筛选
    let filteredData = [...mockData]
    
    // 简单的筛选逻辑
    Object.keys(queryForm.value).forEach(key => {
      const value = queryForm.value[key]
      if (value && typeof value === 'string' && value.trim()) {
        filteredData = [
          filteredData[0], // 保留表头
          ...filteredData.slice(1).filter(row =>
            row.some(cell =>
              cell && cell.toString().toLowerCase().includes(value.toLowerCase())
            )
          )
        ]
      }
    })
    
    tableData.value = filteredData
    ElMessage.success(`查询完成，共找到 ${filteredData.length - 1} 条记录`)
  } finally {
    loading.value = false
  }
}

// 处理重置
function handleReset() {
  queryForm.value = {
    accountLongText: '',
    subjectCategory1: '',
    subjectCategory2: '',
    subjectDirection: ''
  }
  tableData.value = []
  hasQueried.value = false
}



// 处理数据变化
function handleDataChange(newData) {
  tableData.value = newData
}

// 计算表格尺寸
function calculateTableSize() {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  tableWidth.value = Math.max(800, windowWidth - 100)
  tableHeight.value = Math.max(400, windowHeight - 450)
}

// 窗口大小变化处理
function handleResize() {
  calculateTableSize()
}

// 组件挂载
onMounted(() => {
  calculateTableSize()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.subject-mapping-query-component {
  padding: 20px;
  background: #f8f9fb;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.back-button {
  flex-shrink: 0;
}

.query-panel {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

.query-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.action-panel {
  background: #fff;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-left .result-count {
  font-size: 14px;
  color: #666;
}

.action-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.info-text {
  font-size: 14px;
  color: #909399;
  font-style: italic;
}

.result-panel {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.query-table {
  margin-top: 16px;
}

.no-data,
.no-query {
  padding: 40px 0;
  text-align: center;
}
</style>
