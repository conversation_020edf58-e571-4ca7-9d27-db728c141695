<template>
  <div class="vtable-component">
    <!-- 筛选面板 -->
    <div v-if="showFilter" class="filter-panel">
      <div class="filter-conditions">
        <div v-for="(condition, index) in filterConditions" :key="index" class="filter-row">
          <div class="filter-item">
            <select v-model="condition.column">
              <option value="">所有列</option>
              <option
                v-for="(col, colIndex) in columns"
                :key="colIndex"
                :value="colIndex.toString()"
              >
                {{ col.title }}
              </option>
            </select>
          </div>
          <div class="filter-item">
            <select v-model="condition.operator">
              <option value="contains">包含</option>
              <option value="equals">等于</option>
              <option value="starts_with">开头是</option>
              <option value="ends_with">结尾是</option>
              <option value="greater_than">大于</option>
              <option value="less_than">小于</option>
            </select>
          </div>
          <div class="filter-item">
            <input v-model="condition.value" placeholder="输入关键词" @keyup.enter="doFilter" />
          </div>
          <button v-if="filterConditions.length > 1" class="action-btn remove-condition-btn" @click="removeFilterCondition(index)" title="移除此条件">
            ➖
          </button>
        </div>
        <div class="filter-actions">
          <button class="action-btn add-condition-btn" @click="addFilterCondition" title="添加筛选条件">
            ➕ 添加条件
          </button>
          <div class="filter-item logical-operator">
            <label for="logical-operator">条件关系:</label>
            <select id="logical-operator" v-model="logicalOperator">
              <option value="AND">AND (所有条件都满足)</option>
              <option value="OR">OR (任一条件满足)</option>
            </select>
          </div>
          <button class="primary-btn" @click="doFilter">筛选</button>
          <button class="default-btn" @click="resetFilter">重置</button>
        </div>
      </div>
      <!-- 功能按钮区域 -->
      <div class="action-section" v-if="editable || enableCopyPaste">
        <button v-if="enableCopyPaste" class="action-btn" @click="handleCopy" title="复制选中内容">
          📋 复制
        </button>
        <button v-if="enableCopyPaste" class="action-btn" @click="handlePaste" title="粘贴内容">
          📄 粘贴
        </button>
        <button v-if="editable" class="action-btn delete-btn" @click="handleDeleteRows" title="删除选中行">
          🗑️ 删除行
        </button>
        <button v-if="editable" class="action-btn add-btn" @click="handleAddRow" title="添加新行">
          ➕ 添加行
        </button>
        <button v-if="autoWidth" class="action-btn" @click="handleAutoFitColumns" title="自动调整所有列宽">
          📏 自适应列宽
        </button>
        <input
          v-if="enableExcelImport"
          ref="excelFileInput"
          type="file"
          accept=".xlsx,.xls"
          @change="handleExcelImport"
          style="display: none"
        />
        <button
          v-if="enableExcelImport"
          class="action-btn import-btn"
          @click="triggerExcelImport"
          title="导入Excel文件"
        >
          📥 导入Excel
        </button>
        <button class="action-btn export-btn" @click="handleExportExcel" title="导出Excel文件">
          📊 导出Excel
        </button>
        <button class="action-btn" @click="handleExportData" title="导出CSV数据">
          💾 导出CSV
        </button>
        <button
          v-if="enablePushUpdate"
          class="action-btn push-btn"
          @click="handlePushUpdate"
          :disabled="pushLoading"
          title="推送数据到后台更新"
        >
          {{ pushLoading ? '⏳ 推送中...' : '🔄 推送更新' }}
        </button>
      </div>
    </div>
    
    <!-- 表格容器 -->
    <div 
      ref="tableRef" 
      :style="{ width: width + 'px', height: height + 'px' }"
      class="table-container"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'
import * as VTable from '@visactor/vtable'
import { InputEditor, DateInputEditor, ListEditor } from '@visactor/vtable-editors'
import * as ExcelJS from 'exceljs'
import axios from 'axios'

// Props定义
const props = defineProps({
  // 二维数组数据，第一行为标题
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  // 表格宽度
  width: {
    type: Number,
    default: 600
  },
  // 表格高度
  height: {
    type: Number,
    default: 300
  },
  // 是否显示筛选面板
  showFilter: {
    type: Boolean,
    default: true
  },
  // 额外的表格配置
  tableOptions: {
    type: Object,
    default: () => ({})
  },
  // 是否启用编辑功能
  editable: {
    type: Boolean,
    default: true
  },
  // 是否启用复制粘贴
  enableCopyPaste: {
    type: Boolean,
    default: true
  },
  // 是否自动调整列宽
  autoWidth: {
    type: Boolean,
    default: true
  },
  // 是否启用Excel导入功能
  enableExcelImport: {
    type: Boolean,
    default: false
  },
  // 是否启用后台推送更新功能
  enablePushUpdate: {
    type: Boolean,
    default: false
  },
  // 后台推送更新的API端点
  pushUpdateEndpoint: {
    type: String,
    default: ''
  },
  // 表名（用于后台推送）
  tableName: {
    type: String,
    default: ''
  }
})

// 响应式数据
const tableRef = ref()
const excelFileInput = ref()
const filterConditions = ref([{ column: '', operator: 'contains', value: '' }])
const logicalOperator = ref('AND') // 'AND' or 'OR'
const pushLoading = ref(false)

const columns = ref([])
const records = ref([])
const originalRecords = ref([])
let vtableInstance = null

// 标记是否正在进行自定义粘贴操作
let isCustomPasting = false

// 注册编辑器 - 按照官方文档的正确方式
try {
  // 创建编辑器实例
  const inputEditor = new InputEditor();
  const dateInputEditor = new DateInputEditor();
  const listEditor = new ListEditor({ values: ['是', '否'] });

  // 注册编辑器到VTable
  VTable.register.editor('input-editor', inputEditor);
  VTable.register.editor('date-input-editor', dateInputEditor);
  VTable.register.editor('list-editor', listEditor);

  console.log('编辑器注册成功');
} catch (error) {
  console.error('编辑器注册失败:', error);
}

// 基础表格配置 - 增强版
const getBaseTableOptions = () => ({
  theme: VTable.themes.BRIGHT,
  // 自动列宽配置
  widthMode: props.autoWidth ? 'autoWidth' : 'standard',
  autoFillWidth: false,
  limitMaxAutoWidth: 300, // 限制最大自动列宽
  limitMinWidth: 60, // 限制最小列宽

  // 编辑配置 - 使用VTable 1.18.4的正确API
  editCellTrigger: props.editable ? 'doubleclick' : 'none',

  // 复制粘贴配置
  keyboardOptions: {
    copySelected: props.enableCopyPaste,
    // 完全禁用VTable的默认粘贴处理，使用自定义逻辑
    pasteValueToCell: false,
    selectAllOnCtrlA: true,
    moveEditCellOnArrowKeys: true,
    moveEditCellOnEnter: true,
    moveFocusCellOnEnter: false,
    editCellOnEnter: false
  },

  // 选择配置
  select: {
    headerSelectMode: 'inline',
    disableHeaderSelect: false,
    disableSelect: false
  },

  // 样式配置
  autoWrapText: false,
  headerStyle: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: 'bold',
    lineHeight: 20,
    paddingTop: 4,
    paddingBottom: 4
  },
  bodyStyle: {
    fontSize: 11,
    textAlign: 'left',
    lineHeight: 18,
    paddingTop: 3,
    paddingBottom: 3
  },

  // 布局配置
  frozenColCount: 1, // 冻结第一列
  defaultColWidth: 120,
  rowHeight: 32, // 设置行高
  headerRowHeight: 36, // 表头行高

  // 滚动配置
  scrollOption: {
    horizontalMode: 'always',
    verticalMode: 'auto',
    scrollbarSize: 12,
    scrollbarFadeOut: false,
    scrollbarVisible: true,
    wheelDeltaX: 60,
    wheelDeltaY: 60
  },

  // 工具提示
  tooltip: {
    enable: true,
    placement: 'top',
    trigger: 'hover',
    showDelay: 500,
    hideDelay: 200
  }
})

// 从二维数组生成列配置和记录
function generateTableData(data) {
  if (!data || data.length === 0) {
    columns.value = []
    records.value = []
    originalRecords.value = []
    return
  }

  // 第一行作为标题
  const headers = data[0]
  const dataRows = data.slice(1)

  // 智能检测列数据类型
  const detectColumnType = (columnIndex) => {
    let isNumeric = true;
    let isDate = true;
    let hasOptions = false;
    const uniqueValues = new Set();

    for (let i = 0; i < Math.min(dataRows.length, 20); i++) {
      const cellValue = dataRows[i][columnIndex];
      const strValue = String(cellValue || '').trim();

      if (strValue) {
        uniqueValues.add(strValue);

        // 检查是否是数字
        if (isNumeric && isNaN(Number(cellValue)) && cellValue !== null && cellValue !== undefined) {
          isNumeric = false;
        }

        // 检查是否可能是日期,检查有问题
        //if (isDate && !/^\d{4}[-/]\d{1,2}[-/]\d{1,2}/.test(strValue)) {
        //isDate = false;
        //}
      }
    }

    // 如果唯一值少于10个且不是数字/日期，可能适合下拉选择
    hasOptions = uniqueValues.size <= 10 && uniqueValues.size > 1 && !isNumeric && !isDate;
    isDate = false;
    return {
      isNumeric,
      isDate,
      hasOptions,
      uniqueValues: Array.from(uniqueValues)
    };
  };

  // 智能计算列宽度
  const calculateColumnWidth = (columnIndex) => {
    if (!props.autoWidth) {
      return 120; // 默认宽度
    }

    const title = headers[columnIndex] || '';
    const titleLength = title.toString().length;

    let maxContentLength = 0;
    for (let i = 0; i < Math.min(dataRows.length, 10); i++) {
      const cellValue = dataRows[i][columnIndex];
      const strValue = String(cellValue || '');
      maxContentLength = Math.max(maxContentLength, strValue.length);
    }

    const { isNumeric, isDate } = detectColumnType(columnIndex);

    // 根据类型和长度设置宽度
    if (isNumeric) {
      return Math.max(80, Math.min(maxContentLength * 8 + 20, 150));
    } else if (isDate) {
      return 120;
    } else {
      const baseWidth = Math.max(titleLength, maxContentLength) * 8 + 20;
      return Math.min(Math.max(100, baseWidth), 250);
    }
  };

  // 生成列配置
  columns.value = headers.map((title, index) => {
    const width = calculateColumnWidth(index);
    const columnType = detectColumnType(index);

    const columnConfig = {
      field: index.toString(),
      title: title,
      cellType: 'text',
      style: {
        textAlign: columnType.isNumeric ? 'right' : 'left',
        padding: [4, 8, 4, 8]
      }
    };

    // 设置列宽
    if (props.autoWidth) {
      columnConfig.minWidth = 60;
      columnConfig.maxWidth = 300;
    } else {
      columnConfig.width = width;
      columnConfig.minWidth = Math.min(width, 120);
    }

    // 配置编辑器 - 按照官方文档的正确方式
    if (props.editable) {
      // 根据数据类型配置编辑器
      if (columnType.isDate) {
        columnConfig.editor = 'date-input-editor';
      } else if (columnType.hasOptions && columnType.uniqueValues.length > 0) {
        columnConfig.editor = 'list-editor';
      } else {
        columnConfig.editor = 'input-editor';
      }
    }

    return columnConfig;
  });

  // 生成记录数据
  const recordsData = dataRows.map(row => {
    const record = {}
    row.forEach((cell, index) => {
      record[index.toString()] = cell
    })
    return record
  })

  records.value = [...recordsData]
  originalRecords.value = [...recordsData]
}

// 初始化表格
function initTable() {
  if (!tableRef.value || columns.value.length === 0) return

  // 获取基础配置
  const baseOptions = getBaseTableOptions();

  // 合并配置
  const options = {
    ...baseOptions,
    ...props.tableOptions,
    columns: columns.value,
    records: records.value,
    container: tableRef.value,
    width: props.width,
    height: props.height
  };

  // 调试输出
  console.log('VTable配置:', {
    editCellTrigger: options.editCellTrigger,
    editable: props.editable,
    columnsWithEditor: columns.value.filter(col => col.editor).length,
    columns: columns.value.map(col => ({ title: col.title, editor: col.editor }))
  });

  // 创建表格实例
  try {
    vtableInstance = new VTable.ListTable(tableRef.value, options)

    // 添加事件监听
    setupEventListeners();

    // 在实例创建后，确保DOM更新和布局计算完成后再更新滚动条和尺寸
    nextTick(() => {
      if (vtableInstance) {
        vtableInstance.resize()
        vtableInstance.updateScrollBar()

        // 延迟再次更新，确保滚动条正常显示
        setTimeout(() => {
          if (vtableInstance) {
            vtableInstance.updateScrollBar()
          }
        }, 300)
      }
    })
  } catch (error) {
    console.error('创建VTable实例失败:', error)
  }
}

// 设置事件监听器
function setupEventListeners() {
  if (!vtableInstance) return;

  // 监听单元格编辑开始事件
  vtableInstance.on('start_edit_cell', (args) => {
    console.log('开始编辑单元格:', args);
  });

  // 监听单元格编辑结束事件
  vtableInstance.on('end_edit_cell', (args) => {
    console.log('结束编辑单元格:', args);
  });

  // 监听单元格值变化事件
  vtableInstance.on('change_cell_value', (args) => {
    // 如果正在进行自定义粘贴，跳过这个事件处理
    if (isCustomPasting) {
      console.log('跳过自动事件处理，正在进行自定义粘贴');
      return;
    }
    const { col, row, rawValue, oldValue } = args;
    console.log('单元格值变化:', { col, row, newValue: rawValue, oldValue });

    // 获取列的字段名，VTable的记录是基于字段名(field)的
    const field = columns.value[col]?.field;
    if (!field) {
      console.error(`在列配置中找不到索引为 ${col} 的列`);
      return;
    }

    // 更新内部数据状态
    // 注意：这里的 'row' 是指在当前显示数据(records.value)中的行号
    if (records.value[row]) {
      records.value[row][field] = rawValue;
    }

    // 更新原始数据备份，确保与当前表格数据一致
    // 这是一个简化的处理，假设编辑时未进行筛选和排序
    // TODO: 建立一个从显示行到原始行的可靠映射
    if (originalRecords.value[row]) {
      originalRecords.value[row][field] = rawValue;
    }

    // 使用防抖机制延迟触发数据变化事件，避免频繁的表格重建
    debouncedEmitDataChange();
    emit('cell-edit', { col, row, newValue: rawValue, oldValue });
  });

  // 监听复制事件
  vtableInstance.on('copy_data', (args) => {
    // 检查当前组件是否可见（防止多个v-show的VTable组件冲突）
    if (!isComponentVisible()) {
      console.log('VTable组件不可见，跳过复制事件处理');
      return;
    }

    console.log('复制数据:', args);
    emit('copy', args);
  });

  // 监听粘贴事件 - 这个事件在VTable内部粘贴完成后触发
  vtableInstance.on('paste_data', (args) => {
    // 检查当前组件是否可见（防止多个v-show的VTable组件冲突）
    if (!isComponentVisible()) {
      console.log('🚫 VTable组件不可见，跳过粘贴事件处理');
      return;
    }

    console.log('📋 VTable内部粘贴事件触发:', args);

    // 由于我们已经禁用了默认粘贴行为，这个事件应该很少触发
    // 如果触发了，说明可能有其他地方调用了VTable的粘贴方法
    console.warn('⚠️ VTable内部粘贴事件被触发，这可能不是预期行为');

    // 粘贴后更新数据
    safeEmitDataChange();
    emit('paste', args);
  });

  // 监听选择变化
  vtableInstance.on('selected_cell', (args) => {
    console.log('🎯 选择单元格:', args);

    // 添加详细的选择信息
    if (args && typeof args.col !== 'undefined' && typeof args.row !== 'undefined') {
      const { col, row } = args;
      console.log(`📍 选中位置: 行${row} 列${col}`);

      // 显示对应的字段和值
      const field = columns.value[col]?.field;
      const currentValue = records.value[row] ? records.value[row][field] : 'undefined';
      console.log(`📋 字段"${field}" 当前值: "${currentValue}"`);
    }
  });

  // 监听双击事件（用于调试编辑触发）
  vtableInstance.on('dblclick_cell', (args) => {
    console.log('双击单元格:', args);
    if (props.editable) {
      console.log('应该开始编辑');
    }
  });

  // 监听键盘事件，特别处理复制粘贴
  vtableInstance.on('keydown', (args) => {
    // 检查当前组件是否可见（防止多个v-show的VTable组件冲突）
    if (!isComponentVisible()) {
      console.log('🚫 VTable组件不可见，跳过键盘事件处理');
      return;
    }

    if (args.event && args.event.ctrlKey) {
      if (args.event.key === 'v') {
        console.log('⌨️ 检测到 Ctrl+V 粘贴事件');
        // 阻止默认行为，使用我们的自定义粘贴逻辑
        args.event.preventDefault();
        console.log('🛑 已阻止默认粘贴行为，使用自定义逻辑');
        handlePaste();
      } else if (args.event.key === 'c') {
        console.log('⌨️ 检测到 Ctrl+C 复制事件');
        // 让VTable处理复制，但确保只有可见组件响应
        // 不阻止默认行为，让VTable自己处理复制
      }
    }
  });
}

// 定义事件发射
const emit = defineEmits(['data-change', 'cell-edit', 'copy', 'paste'])

// 发射数据变化事件
function emitDataChange() {
  // 将表格数据转换回二维数组格式
  const headers = columns.value.map(col => col.title);
  const dataRows = records.value.map(record =>
    columns.value.map(col => record[col.field])
  );
  const newData = [headers, ...dataRows];

  emit('data-change', newData);
}

// 安全的数据变化事件发射器（带内部更新标记）
function safeEmitDataChange() {
  isInternalUpdate = true;
  emitDataChange();
  nextTick(() => {
    isInternalUpdate = false;
  });
}

// 防抖的数据变化事件发射器
let debounceTimer = null;
function debouncedEmitDataChange() {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  debounceTimer = setTimeout(() => {
    safeEmitDataChange();
    debounceTimer = null;
  }, 100); // 100ms 防抖延迟
}

// Helper function to check if a record satisfies a single condition
function checkCondition(record, condition) {
  const { column, operator, value } = condition;
  const filterValue = String(value || '').trim().toLowerCase();

  if (!filterValue) {
    return true; // Empty filter value means this condition is always true
  }

  // If no specific column is selected, check all columns
  const columnsToCheck = column === '' ? Object.keys(record) : [column];

  for (const colField of columnsToCheck) {
    const cellValue = String(record[colField] || '').toLowerCase();

    switch (operator) {
      case 'contains':
        if (cellValue.includes(filterValue)) return true;
        break;
      case 'equals':
        if (cellValue === filterValue) return true;
        break;
      case 'starts_with':
        if (cellValue.startsWith(filterValue)) return true;
        break;
      case 'ends_with':
        if (cellValue.endsWith(filterValue)) return true;
        break;
      case 'greater_than':
        // Attempt numeric comparison
        const numCellValue = parseFloat(cellValue);
        const numFilterValue = parseFloat(filterValue);
        if (!isNaN(numCellValue) && !isNaN(numFilterValue) && numCellValue > numFilterValue) return true;
        break;
      case 'less_than':
        // Attempt numeric comparison
        const numCellValueLt = parseFloat(cellValue);
        const numFilterValueLt = parseFloat(filterValue);
        if (!isNaN(numCellValueLt) && !isNaN(numFilterValueLt) && numCellValueLt < numFilterValueLt) return true;
        break;
      default:
        // Fallback for unknown operators, treat as contains
        if (cellValue.includes(filterValue)) return true;
        break;
    }
  }
  return false; // No match found for this record with this condition
}

// 筛选功能
function doFilter() {
  const activeConditions = filterConditions.value.filter(c => c.value.trim() !== '');

  if (activeConditions.length === 0) {
    records.value = [...originalRecords.value]
  } else {
    records.value = originalRecords.value.filter(row => {
      if (logicalOperator.value === 'AND') {
        // All conditions must be true
        return activeConditions.every(condition => checkCondition(row, condition));
      } else { // OR
        // At least one condition must be true
        return activeConditions.some(condition => checkCondition(row, condition));
      }
    })
  }

  // 更新表格数据
  if (vtableInstance) {
    vtableInstance.setRecords(records.value);
    vtableInstance.render(true);
  }
}

// 重置筛选
function resetFilter() {
  filterConditions.value = [{ column: '', operator: 'contains', value: '' }];
  logicalOperator.value = 'AND';
  records.value = [...originalRecords.value]
  
  if (vtableInstance) {
    vtableInstance.setRecords(records.value)
    vtableInstance.render(true)
  }
}

// Add a new filter condition row
function addFilterCondition() {
  filterConditions.value.push({ column: '', operator: 'contains', value: '' });
}

// Remove a filter condition row
function removeFilterCondition(index) {
  if (filterConditions.value.length > 1) {
    filterConditions.value.splice(index, 1);
  }
}

// 标记是否正在进行内部数据更新（避免循环更新）
let isInternalUpdate = false;

// 监听数据变化
watch(() => props.data, (newData, oldData) => {
  // 如果是内部更新触发的，跳过处理
  if (isInternalUpdate) {
    console.log('跳过内部更新触发的数据变化');
    return;
  }

  // 检查是否是真正的外部数据变化
  if (oldData && JSON.stringify(newData) === JSON.stringify(oldData)) {
    console.log('数据内容未变化，跳过更新');
    return;
  }

  console.log('外部数据变化，重新生成表格');
  generateTableData(newData)
  nextTick(() => {
    if (vtableInstance) {
      vtableInstance.release()
    }
    initTable()
  })
}, { deep: true, immediate: true })

// 监听表格尺寸变化
watch([() => props.width, () => props.height], () => {
  if (vtableInstance) {
    nextTick(() => {
      vtableInstance.resize()
      vtableInstance.updateScrollBar()
    })
  }
})

// 全局粘贴事件监听器
const handleGlobalPaste = (event) => {
  // 检查当前组件是否可见且获得焦点
  if (!isComponentVisible()) {
    return; // 不是当前组件，不处理
  }

  // 检查事件目标是否在当前VTable容器内
  if (vtableInstance) {
    const container = vtableInstance.getContainer();
    if (container && (container.contains(event.target) || container === event.target)) {
      console.log('🎯 全局粘贴事件被VTable组件拦截');
      event.preventDefault();
      handlePaste();
    }
  }
};

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initTable()

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleWindowResize)

    // 添加全局粘贴事件监听器
    document.addEventListener('paste', handleGlobalPaste, true)
  })
})

// 组件卸载前清理资源
onUnmounted(() => {
  if (vtableInstance) {
    vtableInstance.release()
    vtableInstance = null
  }

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleWindowResize)

  // 移除全局粘贴事件监听器
  document.removeEventListener('paste', handleGlobalPaste, true)
})

// 检查组件是否可见
const isComponentVisible = () => {
  if (!vtableInstance) {
    console.log('VTable实例不存在');
    return false;
  }

  const container = vtableInstance.getContainer();
  if (!container) {
    console.log('VTable容器不存在');
    return false;
  }

  // 检查元素及其父元素的可见性
  let element = container;
  while (element && element !== document.body) {
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden') {
      console.log('元素或父元素不可见:', element.tagName, element.className);
      return false;
    }
    element = element.parentElement;
  }

  // 检查元素的尺寸
  const rect = container.getBoundingClientRect();
  const isVisible = rect.width > 0 && rect.height > 0;

  if (!isVisible) {
    console.log('元素尺寸为0:', rect);
  }

  return isVisible;
};

// 处理窗口大小变化
const handleWindowResize = () => {
  if (vtableInstance) {
    setTimeout(() => {
      vtableInstance.resize()
      vtableInstance.updateScrollBar()
    }, 200)
  }
}

// 处理复制操作
const handleCopy = () => {
  if (vtableInstance && props.enableCopyPaste) {
    try {
      vtableInstance.copy();
      console.log('复制成功');
    } catch (error) {
      console.error('复制失败:', error);
    }
  }
}

// 处理粘贴操作
const handlePaste = async () => {
  console.log('=== 开始粘贴操作 ===');

  if (!vtableInstance || !props.enableCopyPaste) {
    console.log('❌ 粘贴功能未启用或VTable实例不存在');
    return;
  }

  try {
    // 检查当前组件是否可见（防止多个v-show的VTable组件冲突）
    if (!isComponentVisible()) {
      console.log('❌ VTable组件不可见，跳过粘贴处理');
      return;
    }

    console.log('✅ VTable组件可见，继续粘贴处理');

    // 获取当前选中的单元格
    const selectedRanges = vtableInstance.getSelectedCellRanges();
    if (!selectedRanges || selectedRanges.length === 0) {
      console.warn('❌ 没有选中的单元格，无法粘贴');
      return;
    }

    console.log('✅ 当前选中范围:', selectedRanges);

    // 尝试从剪贴板读取数据
    if (!navigator.clipboard || !navigator.clipboard.readText) {
      console.warn('⚠️ 浏览器不支持剪贴板API，尝试使用默认粘贴');
      try {
        vtableInstance.paste();
        console.log('✅ 默认粘贴执行完成');
      } catch (error) {
        console.error('❌ 默认粘贴失败:', error);
      }
      return;
    }

    const clipboardText = await navigator.clipboard.readText();
    if (!clipboardText) {
      console.warn('❌ 剪贴板为空');
      return;
    }

    console.log('📋 剪贴板内容:', `"${clipboardText}"`);

    // 解析剪贴板数据
    const rows = clipboardText.split(/\r\n|\n|\r/).filter(row => row !== '');
    const pasteData = rows.map(row => row.split('\t'));

    console.log('📊 解析后的粘贴数据:', pasteData);

    // 检查是否为单个值
    const isSingleValue = pasteData.length === 1 && pasteData[0].length === 1;
    console.log('🔍 是否为单个值:', isSingleValue);

    if (isSingleValue && selectedRanges.length === 1) {
      const range = selectedRanges[0];
      // 如果是单个值且只选中一个单元格，使用精确粘贴
      if (range.start.row === range.end.row && range.start.col === range.end.col) {
        console.log('🎯 执行单个值精确粘贴到单元格:', range.start);

        const value = pasteData[0][0];
        let { row, col } = range.start;

        console.log(`📝 原始索引: 行${row} 列${col} 值"${value}"`);
        console.log(`📊 当前records数组长度: ${records.value.length}`);
        console.log(`📊 当前columns数组长度: ${columns.value.length}`);

        // VTable可能包含表头行，所以数据行索引需要减1
        // 检查是否需要调整行索引
        if (row > 0 && row <= records.value.length) {
          row = row - 1; // 调整为数据行索引
          console.log(`🔧 调整后的行索引: ${row} (减去表头行)`);
        }

        // 检查调整后的索引是否有效
        if (row < 0 || row >= records.value.length) {
          console.error(`❌ 调整后行索引${row}超出范围 [0, ${records.value.length - 1}]`);
          console.error(`❌ 原始行索引: ${range.start.row}, records长度: ${records.value.length}`);
          return;
        }

        if (col < 0 || col >= columns.value.length) {
          console.error(`❌ 列索引${col}超出范围 [0, ${columns.value.length - 1}]`);
          return;
        }

        console.log(`📝 最终使用索引: 行${row} 列${col} 值"${value}"`);


        // 设置标记，防止事件冲突
        isCustomPasting = true;

        try {
          // 方法1: 直接更新数据结构（避免changeCellValue的事件冲突）
          const field = columns.value[col]?.field;
          console.log(`🔍 列${col}对应的字段名: "${field}"`);
          console.log(`🔍 目标记录:`, records.value[row]);

          if (field && records.value[row]) {
            console.log(`🔄 使用直接数据更新: 字段"${field}"`);
            console.log(`🔄 原值: "${records.value[row][field]}" -> 新值: "${value}"`);

            // 更新记录数据
            records.value[row][field] = value;

            // 同步更新原始记录
            if (originalRecords.value[row]) {
              originalRecords.value[row][field] = value;
              console.log(`🔄 同步更新原始记录完成`);
            }

            // 重新渲染表格以显示更新
            vtableInstance.setRecords([...records.value]);

            // 触发数据变化事件
            safeEmitDataChange();
            console.log('✅ 直接数据更新成功');
            return;
          } else {
            console.error('❌ 无法找到对应的字段或记录');
            console.error(`❌ field: "${field}", records.value[${row}]:`, records.value[row]);
          }
        } catch (directError) {
          console.error('❌ 直接数据更新失败:', directError);

          // 降级方案: 使用changeCellValue（但不调用emitDataChange避免重复）
          try {
            vtableInstance.changeCellValue(col, row, value);
            console.log('✅ changeCellValue降级方案成功');
            // 注意：不调用emitDataChange()，因为changeCellValue会触发change_cell_value事件
            return;
          } catch (changeError) {
            console.error('❌ changeCellValue降级方案也失败:', changeError);
          }
        } finally {
          // 重置标记
          isCustomPasting = false;
        }
      }
    }

    // 多值粘贴或单值粘贴失败时，使用数据批量更新
    console.log('📦 执行批量数据粘贴');
    const startRange = selectedRanges[0].start;
    console.log('📍 起始位置:', startRange);

    // 调整起始行索引，与单个值粘贴保持一致
    let adjustedStartRow = startRange.row;
    if (adjustedStartRow > 0 && adjustedStartRow <= records.value.length) {
      adjustedStartRow = adjustedStartRow - 1; // 调整为数据行索引
      console.log(`🔧 调整后的起始行索引: ${adjustedStartRow} (减去表头行)`);
    }
    console.log('📍 调整后的起始位置: 行', adjustedStartRow, '列', startRange.col);

    // 更新数据
    let hasChanges = false;
    for (let i = 0; i < pasteData.length; i++) {
      const targetRow = adjustedStartRow + i;
      if (targetRow >= records.value.length || targetRow < 0) {
        console.log(`⚠️ 目标行${targetRow}超出范围，跳过`);
        break;
      }

      for (let j = 0; j < pasteData[i].length; j++) {
        const targetCol = startRange.col + j;
        if (targetCol >= columns.value.length) {
          console.log(`⚠️ 目标列${targetCol}超出范围，跳过`);
          break;
        }

        const field = columns.value[targetCol]?.field;
        if (field && records.value[targetRow]) {
          const newValue = pasteData[i][j];
          const oldValue = records.value[targetRow][field];
          if (oldValue !== newValue) {
            console.log(`🔄 更新 行${targetRow} 列${targetCol} 字段"${field}": "${oldValue}" -> "${newValue}"`);
            records.value[targetRow][field] = newValue;
            hasChanges = true;
          }
        }
      }
    }

    if (hasChanges) {
      // 重新渲染表格
      vtableInstance.setRecords([...records.value]);
      safeEmitDataChange();
      console.log('✅ 批量粘贴完成');
    } else {
      console.log('ℹ️ 没有数据变化');
    }

  } catch (error) {
    console.error('❌ 粘贴操作失败:', error);
    // 最后的降级方案：使用VTable默认粘贴
    try {
      console.log('🔄 尝试使用VTable默认粘贴作为降级方案');
      vtableInstance.paste();
      console.log('✅ 降级粘贴执行完成');
    } catch (fallbackError) {
      console.error('❌ 降级粘贴也失败:', fallbackError);
    }
  }

  console.log('=== 粘贴操作结束 ===');
}

// 处理自动调整列宽
const handleAutoFitColumns = () => {
  if (vtableInstance) {
    try {
      for (let i = 0; i < columns.value.length; i++) {
        vtableInstance.autoFitColumnWidth(i);
      }
      console.log('自动调整列宽完成');
    } catch (error) {
      console.error('自动调整列宽失败:', error);
    }
  }
}

// 处理删除行
const handleDeleteRows = () => {
  if (!vtableInstance) return;

  try {
    const selectedRanges = vtableInstance.getSelectedCellRanges();
    if (!selectedRanges || selectedRanges.length === 0) {
      alert('请先选择要删除的行');
      return;
    }

    // 使用Set存储要删除的行的唯一标识（这里用JSON字符串）
    const rowsToDelete = new Set();
    selectedRanges.forEach(range => {
      for (let i = range.start.row; i <= range.end.row; i++) {
        // 根据观察到的“删除下一行”行为，我们假设行索引从1开始，因此减1来修正
        const recordIndex = i - 1;
        if (recordIndex >= 0 && recordIndex < records.value.length) {
          // 将对象转换为字符串存入Set，以便在原始数据和当前数据中都能匹配
          rowsToDelete.add(JSON.stringify(records.value[recordIndex]));
        }
      }
    });

    if (rowsToDelete.size === 0) {
      alert('没有选中有效的数据行');
      return;
    }

    if (!confirm(`确定要删除选中的 ${rowsToDelete.size} 行吗？`)) {
      return;
    }

    // 从当前记录和原始记录中同时过滤掉要删除的行
    records.value = records.value.filter(
      record => !rowsToDelete.has(JSON.stringify(record))
    );
    originalRecords.value = originalRecords.value.filter(
      record => !rowsToDelete.has(JSON.stringify(record))
    );

    // 更新表格
    vtableInstance.setRecords(records.value);

    safeEmitDataChange();
    console.log(`成功删除 ${rowsToDelete.size} 行`);

  } catch (error) {
    console.error('删除行失败:', error);
    alert('删除行失败，请重试');
  }
};

// 处理添加行 (支持自定义行数)
const handleAddRow = () => {
  if (!vtableInstance || columns.value.length === 0) return;

  // 1. 让用户输入要添加的行数
  const rowCountStr = prompt('请输入要添加的行数', '1');
  if (rowCountStr === null) {
    // 用户点击了取消
    return;
  }
  const rowCount = parseInt(rowCountStr, 10);

  if (isNaN(rowCount) || rowCount <= 0) {
    alert('请输入有效的正整数行数');
    return;
  }

  try {
    // 2. 创建并添加对应数量的新行
    for (let i = 0; i < rowCount; i++) {
      const newRow = {};
      columns.value.forEach(col => {
        newRow[col.field] = '';
      });
      records.value.push(newRow);
      originalRecords.value.push({ ...newRow });
    }

    // 3. 更新表格
    vtableInstance.setRecords(records.value);
    vtableInstance.render(true);

    // 4. 触发数据变化事件
    safeEmitDataChange();

    // 5. 滚动到首个新行并选中
    const firstNewRowIndex = records.value.length - rowCount;
    setTimeout(() => {
      if (vtableInstance) {
        vtableInstance.selectCell(0, firstNewRowIndex);
        vtableInstance.scrollToCell({ col: 0, row: firstNewRowIndex });
      }
    }, 100);

    console.log(`成功添加 ${rowCount} 行`);
  } catch (error) {
    console.error('添加行失败:', error);
    alert('添加行失败，请重试');
  }
};

// 触发Excel导入文件选择
const triggerExcelImport = () => {
  if (excelFileInput.value) {
    excelFileInput.value.click()
  }
}

// 显示导入模式选择对话框
const showImportModeDialog = () => {
  return new Promise((resolve) => {
    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    `

    dialog.innerHTML = `
      <div style="background: white; padding: 24px; border-radius: 12px; min-width: 320px; box-shadow: 0 8px 32px rgba(0,0,0,0.2);">
        <h3 style="margin: 0 0 16px 0; color: #333; font-size: 18px;">选择导入模式</h3>
        <div style="margin: 16px 0;">
          <label style="display: flex; align-items: center; margin-bottom: 12px; cursor: pointer;">
            <input type="radio" name="importMode" value="append" checked style="margin-right: 8px;">
            <span style="color: #333;">追加导入 - 在现有数据后添加新数据</span>
          </label>
          <label style="display: flex; align-items: center; cursor: pointer;">
            <input type="radio" name="importMode" value="overwrite" style="margin-right: 8px;">
            <span style="color: #333;">覆盖导入 - 替换所有现有数据</span>
          </label>
        </div>
        <div style="text-align: right; margin-top: 24px;">
          <button id="cancelImport" style="margin-right: 12px; padding: 8px 16px; border: 1px solid #ddd; background: white; border-radius: 6px; cursor: pointer;">取消</button>
          <button id="confirmImport" style="padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
            确定
          </button>
        </div>
      </div>
    `

    document.body.appendChild(dialog)

    const cancelButton = dialog.querySelector('#cancelImport')
    const confirmButton = dialog.querySelector('#confirmImport')

    cancelButton.onclick = () => {
      document.body.removeChild(dialog)
      resolve(null)
    }

    confirmButton.onclick = () => {
      const selectedMode = dialog.querySelector('input[name="importMode"]:checked').value
      document.body.removeChild(dialog)
      resolve(selectedMode)
    }
  })
}

// 处理Excel导入
const handleExcelImport = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  try {
    // 显示导入模式选择对话框
    const importMode = await showImportModeDialog()
    if (!importMode) {
      // 用户取消了导入
      event.target.value = ''
      return
    }

    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.load(file)

    const worksheet = workbook.getWorksheet(1)
    const importedData = []

    worksheet.eachRow((row, rowNumber) => {
      const rowData = []
      row.eachCell((cell, colNumber) => {
        rowData.push(cell.value ? cell.value.toString() : '')
      })
      importedData.push(rowData)
    })

    if (importedData.length > 0) {
      let finalData

      if (importMode === 'overwrite') {
        // 覆盖模式：直接使用导入的数据
        finalData = importedData
        console.log(`Excel覆盖导入成功，共导入 ${importedData.length - 1} 条记录`)
      } else {
        // 追加模式：合并现有数据和导入数据
        const currentData = props.data || []
        if (currentData.length > 0) {
          // 保留现有表头，追加数据行
          finalData = [...currentData, ...importedData.slice(1)]
        } else {
          // 如果当前没有数据，直接使用导入数据
          finalData = importedData
        }
        console.log(`Excel追加导入成功，共追加 ${importedData.length - 1} 条记录`)
      }

      // 发出数据变化事件
      emit('data-change', finalData)
    } else {
      console.warn('Excel文件为空')
      alert('Excel文件为空，无法导入')
    }
  } catch (error) {
    console.error('Excel导入失败:', error)
    alert('Excel导入失败，请检查文件格式: ' + error.message)
  } finally {
    // 清空文件输入，允许重复选择同一文件
    event.target.value = ''
  }
}

// 处理导出Excel
const handleExportExcel = async () => {
  try {
    const headers = columns.value.map(col => col.title);
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field] || '')
    );

    // Identify ID card columns by header keywords
    const idCardHeaderKeywords = ['身份证号', '证件号码', '公民身份号码'];
    const idColumnFlags = headers.map(header =>
      idCardHeaderKeywords.some(keyword => header.includes(keyword))
    );

    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('数据表');

    // 设置表头
    const headerRow = worksheet.addRow(headers);
    headerRow.eachCell((cell, colNumber) => {
      cell.font = { bold: true, color: { argb: 'FFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // 添加数据行
    dataRows.forEach(row => {
      const dataRow = worksheet.addRow(row);
      dataRow.eachCell((cell, colNumber) => {
        cell.alignment = { horizontal: 'left', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        // 根据列类型和内容设置单元格格式
        let value = cell.value;
        const isIdColumn = idColumnFlags[colNumber - 1];
        const isLongNumeric = typeof value === 'string' && /^\d{15,}$/.test(value.trim());

        if (isIdColumn || isLongNumeric) {
          // 使用文本格式以避免被Excel转换为科学计数法或丢失精度
          cell.numFmt = '@';
          cell.value = value !== null && value !== undefined ? String(value) : '';
        } else if (typeof value === 'string' && !isNaN(value) && value.trim() !== '') {
          // 常规数字格式化
          cell.value = parseFloat(value);
          cell.numFmt = '#,##0.00';
        }
      });
    });

    // 自动调整列宽
    worksheet.columns.forEach((column, index) => {
      let maxLength = headers[index]?.length || 10;
      dataRows.forEach(row => {
        const cellValue = String(row[index] || '');
        maxLength = Math.max(maxLength, cellValue.length);
      });
      column.width = Math.min(Math.max(maxLength + 2, 10), 50);
    });

    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `table_data_${new Date().toISOString().slice(0, 10)}.xlsx`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('导出Excel成功');
  } catch (error) {
    console.error('导出Excel失败:', error);
    alert('导出Excel失败，请重试');
  }
};

// 处理导出CSV数据
const handleExportData = () => {
  try {
    const headers = columns.value.map(col => col.title);
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field])
    );
    const exportData = [headers, ...dataRows];

    // 转换为CSV格式
    const csvContent = exportData.map(row =>
      row.map(cell => `"${String(cell || '').replace(/"/g, '""')}"`).join(',')
    ).join('\n');

    // 创建下载链接
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `table_data_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('导出CSV数据成功');
  } catch (error) {
    console.error('导出CSV数据失败:', error);
  }
}

// 处理推送后台更新
const handlePushUpdate = async () => {
  if (!props.enablePushUpdate || !props.pushUpdateEndpoint) {
    console.warn('推送更新功能未启用或未配置API端点')
    return
  }

  if (records.value.length === 0) {
    console.warn('没有数据可以推送')
    return
  }

  try {
    // 确认对话框
    if (!confirm('确定要将当前数据推送到后台进行更新吗？')) {
      return
    }

    pushLoading.value = true

    // 构造推送数据
    const headers = columns.value.map(col => col.title)
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field] || '')
    )
    const pushData = {
      tableName: props.tableName || '数据表',
      data: [headers, ...dataRows],
      timestamp: new Date().toISOString()
    }

    // 发送推送请求
    const response = await fetch(props.pushUpdateEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(pushData)
    })

    if (response.ok) {
      const result = await response.json()
      if (result.code === 200) {
        console.log('数据推送成功')
        alert('数据推送成功')
      } else {
        throw new Error(result.message || '推送失败')
      }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  } catch (error) {
    console.error('推送失败:', error)
    alert(`推送失败: ${error.message}`)
  } finally {
    pushLoading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  resetFilter,
  doFilter,
  getTableInstance: () => vtableInstance,

  // 数据操作方法
  getData: () => {
    const headers = columns.value.map(col => col.title);
    const dataRows = records.value.map(record =>
      columns.value.map(col => record[col.field])
    );
    return [headers, ...dataRows];
  },

  setData: (newData) => {
    generateTableData(newData);
    if (vtableInstance) {
      vtableInstance.setRecords(records.value);
      vtableInstance.render(true);
    }
  },

  // 行操作方法
  addRow: handleAddRow,
  deleteRows: handleDeleteRows,

  addRowData: (rowData) => {
    if (!vtableInstance || columns.value.length === 0) return;

    const newRow = {};
    columns.value.forEach((col, index) => {
      newRow[col.field] = rowData[index] || '';
    });

    records.value.push(newRow);
    originalRecords.value.push({ ...newRow });

    if (vtableInstance) {
      vtableInstance.setRecords(records.value);
      vtableInstance.render(true);
    }

    safeEmitDataChange();
  },

  deleteRowByIndex: (rowIndex) => {
    if (rowIndex >= 0 && rowIndex < records.value.length) {
      records.value.splice(rowIndex, 1);
      originalRecords.value.splice(rowIndex, 1);

      if (vtableInstance) {
        vtableInstance.setRecords(records.value);
        vtableInstance.render(true);
      }

      safeEmitDataChange();
    }
  },

  // 编辑相关方法
  startEdit: (row, col) => {
    if (vtableInstance && props.editable) {
      vtableInstance.startEditCell(col, row);
    }
  },

  endEdit: () => {
    if (vtableInstance) {
      vtableInstance.completeEdit();
    }
  },

  // 选择相关方法
  selectCell: (row, col) => {
    if (vtableInstance) {
      vtableInstance.selectCell(col, row);
    }
  },

  getSelectedData: () => {
    if (vtableInstance) {
      return vtableInstance.getSelectedData();
    }
    return null;
  },

  getSelectedRows: () => {
    if (!vtableInstance) return [];

    const selectedRanges = vtableInstance.getSelectedCellRanges();
    if (!selectedRanges || selectedRanges.length === 0) return [];

    const selectedRows = new Set();
    selectedRanges.forEach(range => {
      for (let row = range.start.row; row <= range.end.row; row++) {
        if (row >= 0 && row < records.value.length) {
          selectedRows.add(row);
        }
      }
    });

    return Array.from(selectedRows);
  },

  // 复制粘贴方法
  copy: () => {
    if (vtableInstance && props.enableCopyPaste) {
      vtableInstance.copy();
    }
  },

  paste: () => {
    handlePaste();
  },

  // 导出方法
  exportExcel: handleExportExcel,
  exportCSV: handleExportData,

  // 布局方法
  updateScrollBar: () => {
    if (vtableInstance) {
      vtableInstance.updateScrollBar()
      setTimeout(() => {
        if (vtableInstance) {
          vtableInstance.updateScrollBar()
        }
      }, 300)
    }
  },

  resize: () => {
    if (vtableInstance) {
      vtableInstance.resize()
      setTimeout(() => {
        if (vtableInstance) {
          vtableInstance.updateScrollBar()
        }
      }, 300)
    }
  },

  // 列宽调整
  autoFitColumnWidth: (col) => {
    if (vtableInstance) {
      vtableInstance.autoFitColumnWidth(col);
    }
  },

  autoFitAllColumnWidth: () => {
    if (vtableInstance) {
      for (let i = 0; i < columns.value.length; i++) {
        vtableInstance.autoFitColumnWidth(i);
      }
    }
  }
})
</script>

<style scoped>
.vtable-component {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.table-container {
  overflow: visible !important; /* 确保容器不会阻止表格内部的滚动条 */
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding-bottom: 15px; /* 为水平滚动条预留空间 */
}

/* 确保表格内部滚动容器正常工作 */
:deep(.vtable-scroll-container) {
  overflow: auto !important;
}

/* 调整滚动条样式 */
:deep(.vtable-scroll-container::-webkit-scrollbar) {
  width: 10px;
  height: 10px;
}

:deep(.vtable-scroll-container::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.vtable-scroll-container::-webkit-scrollbar-thumb) {
  background: #c0c4cc;
  border-radius: 4px;
}

:deep(.vtable-scroll-container::-webkit-scrollbar-thumb:hover) {
  background: #909399;
}

/* 调整单元格样式 */
:deep(.vtable-body-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filter-conditions {
  display: flex;
  flex-direction: column;
  gap: 10px; /* Space between filter rows */
  margin-bottom: 12px;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-top: 10px;
}

.filter-panel {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  border: 1px solid #e4e7ed;
}

.action-section {
  display: flex;
  gap: 8px;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-item.logical-operator {
  margin-left: auto; /* Push logical operator to the right */
  gap: 8px;
}

.filter-item select,
.filter-item input {
  padding: 6px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 140px;
  font-size: 12px;
  transition: all 0.3s;
}

.filter-item select:focus,
.filter-item input:focus {
  border-color: #409eff;
  outline: none;
}

.primary-btn,
.default-btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.primary-btn {
  background: #409eff;
  color: #fff;
}

.primary-btn:hover {
  background: #66b1ff;
}

.default-btn {
  background: #f4f4f5;
  color: #606266;
}

.default-btn:hover {
  background: #e9e9eb;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: #fff;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
  color: #409eff;
}

.action-btn:active {
  background: #e6f7ff;
  border-color: #409eff;
}

.action-btn.delete-btn {
  background: #f56c6c;
  color: #fff;
  border-color: #f56c6c;
}

.action-btn.delete-btn:hover {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}

.action-btn.add-btn {
  background: #67c23a;
  color: #fff;
  border-color: #67c23a;
}

.action-btn.add-btn:hover {
  background: #85ce61;
  border-color: #85ce61;
  color: #fff;
}

.action-btn.export-btn {
  background: #409eff;
  color: #fff;
  border-color: #409eff;
}

.action-btn.export-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}

.action-btn.import-btn {
  background: #67c23a;
  color: #fff;
  border-color: #67c23a;
}

.action-btn.import-btn:hover {
  background: #85ce61;
  border-color: #85ce61;
  color: #fff;
}

.action-btn.push-btn {
  background: #e6a23c;
  color: #fff;
  border-color: #e6a23c;
}

.action-btn.push-btn:hover {
  background: #ebb563;
  border-color: #ebb563;
  color: #fff;
}

.action-btn.push-btn:disabled {
  background: #c0c4cc;
  border-color: #c0c4cc;
  color: #fff;
  cursor: not-allowed;
}

.action-btn.add-condition-btn,
.action-btn.remove-condition-btn {
  padding: 6px 10px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: #fff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn.add-condition-btn:hover,
.action-btn.remove-condition-btn:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
}

/* 表格编辑状态样式 */
:deep(.vtable-cell-editing) {
  background-color: #e6f7ff !important;
  border: 2px solid #409eff !important;
}

/* 选中单元格样式 */
:deep(.vtable-cell-selected) {
  background-color: #f0f9ff !important;
  border: 1px solid #409eff !important;
}

/* 复制选择区域样式 */
:deep(.vtable-selection-border) {
  border: 2px dashed #67c23a !important;
}

/* 表格行悬停效果 */
:deep(.vtable-body-row:hover) {
  background-color: #f5f7fa !important;
}

</style>
