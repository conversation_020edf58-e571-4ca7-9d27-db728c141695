<template>
  <div class="quick-fill-view">
    <!-- 卡片列表视图 -->
    <div v-if="currentView === 'cards'">
      <h1>速填模板</h1>
      <div class="cards-container">
        <div
          v-for="(card, index) in cards"
          :key="index"
          class="card"
          @click="handleCardClick(card)"
        >
          <div class="card-icon">
            <component :is="card.icon" />
          </div>
          <div class="card-content">
            <h3>{{ card.title }}</h3>
            <p>{{ card.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 各种速填组件视图 -->
    <div v-else-if="currentView === 'invoice-entry'">
      <InvoiceEntryComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'material-settlement'">
      <MaterialSettlementComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'material-outbound'">
      <MaterialOutboundComponent @back="goBack" />
    </div>
    <div v-else-if="currentView === 'dispatch-salary'">
      <DispatchSalaryComponent @back="goBack" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import InvoiceEntryComponent from '@/components/InvoiceEntryComponent.vue'
import MaterialSettlementComponent from '@/components/MaterialSettlementComponent.vue'
import MaterialOutboundComponent from '@/components/MaterialOutboundComponent.vue'
import DispatchSalaryComponent from '@/components/DispatchSalaryComponent.vue'
import {
  Document,
  Box,
  Van,
  Money
} from '@element-plus/icons-vue'

const router = useRouter()

// 当前视图状态
const currentView = ref('cards')

// 卡片功能数据
const cards = [
  {
    title: '发票速录',
    description: '快速录入发票信息',
    icon: Document,
    type: 'invoice-entry'
  },
  {
    title: '物资结算',
    description: '物资采购结算处理',
    icon: Box,
    type: 'material-settlement'
  },
  {
    title: '物资出库',
    description: '物资出库单据处理',
    icon: Van,
    type: 'material-outbound'
  },
  {
    title: '派遣工资',
    description: '派遣人员工资处理',
    icon: Money,
    type: 'dispatch-salary'
  }
]

// 处理卡片点击
function handleCardClick(card) {
  currentView.value = card.type
}

// 返回卡片列表
function goBack() {
  currentView.value = 'cards'
}
</script>

<style>
body {
  background: #f8f9fb;
}

.quick-fill-view {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

h1 {
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 32px;
  color: #222;
  letter-spacing: 2px;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  width: 100%;
  max-width: 1200px;
}

.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-icon {
  font-size: 2.5rem;
  color: #409eff;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #f0f8ff;
  border-radius: 50%;
}

.card-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.card-content p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.temp-component {
  padding: 40px;
  text-align: center;
  background: white;
  border-radius: 8px;
  margin: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.temp-component h2 {
  color: #333;
  margin-bottom: 20px;
}

.temp-component button {
  background: #409eff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 20px;
}

.temp-component button:hover {
  background: #337ecc;
}
</style>
