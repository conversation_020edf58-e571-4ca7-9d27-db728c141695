<template>
  <div class="view-container">
    <div class="section-header">
      <h2>综合查询系统</h2>
      <p>多表联合查询与数据分析</p>
    </div>

    <div class="action-buttons">
      <button @click="executeQuery" class="action-button fetch" :disabled="loading">
        {{ loading ? '查询中...' : '执行查询' }}
      </button>
      <button @click="clearResults" class="action-button save">清空结果</button>
      <button @click="toggleRuleConfig" class="action-button export">
        {{ showRuleConfig ? '隐藏规则' : '显示规则' }}
      </button>
      <button @click="loadTemplate('科目对照查询')" class="action-button fetch">科目对照</button>
      <button @click="loadTemplate('合同金额查询')" class="action-button fetch">合同查询</button>
      <button @click="loadTemplate('异常数据查询')" class="action-button fetch">异常数据</button>

      <div class="table-selector">
        <select v-model="selectedTable" @change="onTableChange" class="table-select">
          <option value="">选择数据表</option>
          <option
            v-for="table in availableTables"
            :key="table.value"
            :value="table.value"
          >
            {{ table.label }}
          </option>
        </select>
      </div>

      <button @click="exportResults" class="action-button export">导出结果</button>
      <button @click="triggerFileInput" class="action-button export">导入Excel</button>
      <input ref="fileInput" type="file" accept=".xlsx,.xls" style="display:none" @change="importExcel" />
    </div>

    <!-- 查询规则配置面板 -->
    <div v-show="showRuleConfig" class="rule-config-panel">
      <div class="rule-config-header">
        <h3>查询规则配置</h3>
        <button @click="addQueryRule" class="action-button fetch">添加规则</button>
      </div>

      <div class="rules-container">
        <div
          v-for="(rule, index) in queryRules"
          :key="rule.id"
          class="rule-item"
        >
          <div class="rule-controls">
            <select
              v-model="rule.table"
              @change="onRuleTableChange(rule)"
              class="rule-select"
            >
              <option value="">选择数据表</option>
              <option
                v-for="table in availableTables"
                :key="table.value"
                :value="table.value"
              >
                {{ table.label }}
              </option>
            </select>

            <select
              v-model="rule.field"
              class="rule-select"
              :disabled="!rule.table"
            >
              <option value="">选择字段</option>
              <option
                v-for="field in getFieldsForTable(rule.table)"
                :key="field.value"
                :value="field.value"
              >
                {{ field.label }}
              </option>
            </select>

            <select
              v-model="rule.operator"
              class="rule-select"
            >
              <option value="eq">等于</option>
              <option value="like">包含</option>
              <option value="gt">大于</option>
              <option value="lt">小于</option>
              <option value="gte">大于等于</option>
              <option value="lte">小于等于</option>
              <option value="ne">不等于</option>
              <option value="null">为空</option>
              <option value="not_null">不为空</option>
            </select>

            <input
              v-model="rule.value"
              placeholder="输入值"
              class="rule-input"
              :disabled="rule.operator === 'null' || rule.operator === 'not_null'"
            />

            <select
              v-model="rule.logic"
              class="rule-select logic-select"
              v-if="index < queryRules.length - 1"
            >
              <option value="and">AND</option>
              <option value="or">OR</option>
            </select>

            <button
              @click="removeQueryRule(index)"
              class="action-button export"
            >
              删除
            </button>
          </div>
        </div>
      </div>

      <!-- 预设查询模板 -->
      <div class="preset-templates">
        <h4>预设查询模板</h4>
        <div class="template-buttons">
          <button
            v-for="template in queryTemplates"
            :key="template.name"
            @click="loadTemplate(template.name)"
            class="template-button"
          >
            {{ template.name }}
          </button>
        </div>
      </div>
    </div>

    <div ref="container" class="table-container"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { createUniver, defaultTheme, LocaleType, merge, Univer, FUniver } from '@univerjs/presets'
import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core'
import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-core.css'

import { UniverSheetsConditionalFormattingPreset } from '@univerjs/presets/preset-sheets-conditional-formatting'
import sheetsConditionalFormattingZhCN from '@univerjs/presets/preset-sheets-conditional-formatting/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-conditional-formatting.css'

import { UniverSheetsDataValidationPreset } from '@univerjs/presets/preset-sheets-data-validation'
import sheetsDataValidationZhCN from '@univerjs/presets/preset-sheets-data-validation/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-data-validation.css'

import { UniverSheetsFilterPreset } from '@univerjs/presets/preset-sheets-filter'
import sheetsFilterZhCN from '@univerjs/presets/preset-sheets-filter/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-filter.css'

import * as ExcelJS from 'exceljs'

// 响应式数据
const container = ref<HTMLElement | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)
const showRuleConfig = ref(false)
const loading = ref(false)
const queryRules = ref([])
const selectedTable = ref('')

// Univer实例
let univerInstance: Univer | null = null
let univerAPIInstance: FUniver | null = null

// 可用的数据表配置
const availableTables = ref([
  { label: '科目对照', value: 'subject_mapping' },
  { label: '异常数据', value: 'exception_data' },
  { label: '一体化合同台账', value: 'integrated_contract' },
  { label: '主数据', value: 'master_data' },
  { label: '付款台账', value: 'payment_ledger' },
  { label: '担保台账', value: 'guarantee_ledger' },
  { label: '内部银行', value: 'internal_bank' },
  { label: '内部对账', value: 'internal_reconciliation' }
])

// 表字段配置
const tableFields = ref({
  subject_mapping: [
    { label: '总账科目长文本', value: 'gl_account_text' },
    { label: '科目分类1', value: 'category1' },
    { label: '科目分类2', value: 'category2' },
    { label: '科目方向', value: 'direction' }
  ],
  exception_data: [
    { label: '异常类型', value: 'exception_type' },
    { label: '异常描述', value: 'description' },
    { label: '发生日期', value: 'occur_date' },
    { label: '处理状态', value: 'status' }
  ],
  integrated_contract: [
    { label: '合同编号', value: 'contract_number' },
    { label: '合同名称', value: 'contract_name' },
    { label: '客商名称', value: 'customer_name' },
    { label: '合同金额', value: 'contract_amount' },
    { label: '合同类型', value: 'contract_type' }
  ],
  master_data: [
    { label: '项目编码', value: 'project_code' },
    { label: '利润中心', value: 'profit_center' },
    { label: '核算组织', value: 'accounting_org' },
    { label: '利润中心描述', value: 'profit_center_desc' }
  ]
})

// 预设查询模板
const queryTemplates = ref({
  '科目对照查询': {
    name: '科目对照查询',
    rules: [
      {
        id: Date.now(),
        table: 'subject_mapping',
        field: 'category1',
        operator: 'like',
        value: '',
        logic: 'and'
      }
    ]
  },
  '合同金额查询': {
    name: '合同金额查询',
    rules: [
      {
        id: Date.now(),
        table: 'integrated_contract',
        field: 'contract_amount',
        operator: 'gte',
        value: '100000',
        logic: 'and'
      }
    ]
  },
  '异常数据查询': {
    name: '异常数据查询',
    rules: [
      {
        id: Date.now(),
        table: 'exception_data',
        field: 'status',
        operator: 'eq',
        value: '未处理',
        logic: 'and'
      }
    ]
  }
})

// 方法定义
// 切换规则配置面板
function toggleRuleConfig() {
  showRuleConfig.value = !showRuleConfig.value
}

// 添加查询规则
function addQueryRule() {
  queryRules.value.push({
    id: Date.now(),
    table: '',
    field: '',
    operator: 'eq',
    value: '',
    logic: 'and'
  })
}

// 删除查询规则
function removeQueryRule(index: number) {
  queryRules.value.splice(index, 1)
}

// 表变化时清空字段选择
function onTableChange() {
  // 当主表选择器变化时的处理
  console.log('Selected table changed:', selectedTable.value)
}

// 规则表变化时清空字段选择
function onRuleTableChange(rule: any) {
  rule.field = ''
}

// 获取指定表的字段列表
function getFieldsForTable(tableName: string) {
  return tableFields.value[tableName] || []
}

// 加载查询模板
function loadTemplate(templateName: string) {
  const template = queryTemplates.value[templateName]
  if (template) {
    queryRules.value = JSON.parse(JSON.stringify(template.rules))
    showRuleConfig.value = true
    alert(`已加载模板：${template.name}`)
  }
}

// 执行查询
async function executeQuery() {
  if (queryRules.value.length === 0) {
    alert('请先添加查询规则')
    return
  }

  // 验证查询规则
  const invalidRules = queryRules.value.filter((rule: any) =>
    !rule.table || !rule.field || !rule.operator ||
    (rule.operator !== 'null' && rule.operator !== 'not_null' && !rule.value)
  )

  if (invalidRules.length > 0) {
    alert('请完善所有查询规则的配置')
    return
  }

  loading.value = true

  try {
    const response = await fetch('http://localhost:8000/api/universal-query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        rules: queryRules.value,
        table: selectedTable.value
      })
    })

    if (!response.ok) {
      throw new Error(`查询失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    if (result.success) {
      // 将查询结果加载到Univer中
      await loadDataToUniver(result.data)
      alert(`查询完成，共找到 ${result.data.length - 1} 条记录`)
    } else {
      throw new Error(result.message || '查询失败')
    }
  } catch (error: any) {
    console.error('查询失败:', error)

    // 如果API失败，使用模拟数据
    const mockData = generateMockData()
    await loadDataToUniver(mockData)
    alert('API调用失败，已加载模拟数据')
  } finally {
    loading.value = false
  }
}

// 清空结果
function clearResults() {
  if (univerAPIInstance) {
    const unitId = univerAPIInstance.getActiveWorkbook()?.getId()
    if (unitId) {
      univerAPIInstance.disposeUnit(unitId)
    }

    // 创建空的工作簿
    const fWorkbook = univerAPIInstance.createWorkbook({
      id: 'universal-query-workbook',
      name: '综合查询结果'
    })
    fWorkbook.create('查询结果', 100, 20)
  }

  alert('已清空查询结果')
}

// 将数据加载到Univer
async function loadDataToUniver(data: any[][]) {
  if (!univerAPIInstance || !data || data.length === 0) return

  try {
    // 清除现有工作簿
    const unitId = univerAPIInstance.getActiveWorkbook()?.getId()
    if (unitId) {
      univerAPIInstance.disposeUnit(unitId)
    }

    // 创建新的工作簿
    const fWorkbook = univerAPIInstance.createWorkbook({
      id: 'universal-query-workbook',
      name: '综合查询结果'
    })

    const sheet = fWorkbook.create('查询结果', Math.max(100, data.length + 10), Math.max(20, data[0]?.length + 5))

    // 设置数据
    if (data.length > 0) {
      sheet.getRange(0, 0, data.length, data[0].length).setValues(data)

      // 设置表头样式
      if (data.length > 0) {
        const headerRange = sheet.getRange(0, 0, 1, data[0].length)
        headerRange.setFontWeight('bold')
        headerRange.setBackgroundColor('#f0f0f0')
      }
    }

    console.log('数据已成功加载到Univer')
  } catch (error: any) {
    console.error('加载数据到Univer失败:', error)
    alert('加载数据失败: ' + error.message)
  }
}

// 生成模拟数据
function generateMockData() {
  const headers = ['ID', '项目名称', '合同编号', '金额', '状态', '创建时间']
  const mockRows = []

  for (let i = 1; i <= 20; i++) {
    mockRows.push([
      i,
      `项目${i}`,
      `HT${String(i).padStart(4, '0')}`,
      (Math.random() * 1000000).toFixed(2),
      Math.random() > 0.5 ? '进行中' : '已完成',
      new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toLocaleDateString()
    ])
  }

  return [headers, ...mockRows]
}

// 导出结果
function exportResults() {
  if (!univerAPIInstance) {
    alert('没有可导出的数据')
    return
  }

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    if (workbook) {
      // 这里可以添加导出逻辑
      alert('导出功能开发中...')
    }
  } catch (error: any) {
    alert('导出失败: ' + error.message)
  }
}

// 触发文件输入
function triggerFileInput() {
  fileInput.value?.click()
}

// 导入Excel
async function importExcel(event: Event) {
  const input = event.target as HTMLInputElement
  if (!input.files || input.files.length === 0) return

  const file = input.files[0]
  const reader = new FileReader()

  reader.onload = async (e) => {
    const buffer = e.target?.result
    if (!buffer) return

    try {
      const workbook = new ExcelJS.Workbook()
      await workbook.xlsx.load(buffer as ArrayBuffer)

      const univerWorkbook = univerAPIInstance?.getActiveWorkbook()
      if (!univerWorkbook) return

      for (const worksheet of workbook.worksheets) {
        // 检查是否存在同名sheet，不存在则创建
        let univerSheet = univerWorkbook.getSheetByName(worksheet.name)
        if (!univerSheet) {
          univerSheet = univerWorkbook.create(worksheet.name, worksheet.rowCount, worksheet.columnCount)
        }

        // 读取数据
        const rows: any[][] = []
        worksheet.eachRow({ includeEmpty: true }, (row) => {
          const rowData: any[] = []
          for (let col = 1; col <= worksheet.columnCount; col++) {
            const cell = row.getCell(col)
            if (cell.formula) {
              const formula = cell.formula.replace(/_xlfn\./g, '')
              rowData.push('=' + formula)
            } else {
              rowData.push(cell.value !== undefined ? cell.value : null)
            }
          }
          rows.push(rowData)
        })

        // 写入数据
        if (univerSheet && rows.length > 0) {
          const range = univerSheet.getRange(0, 0, rows.length, worksheet.columnCount)
          range.setValues(rows)
        }
      }

      alert('Excel导入成功')
      input.value = ''
    } catch (error: any) {
      alert('Excel导入失败: ' + error.message)
    }
  }

  reader.readAsArrayBuffer(file)
}

// 组件挂载
onMounted(async () => {
  // 初始化Univer
  const { univer, univerAPI } = createUniver({
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: merge(
        {},
        UniverPresetSheetsCoreZhCN,
        sheetsConditionalFormattingZhCN,
        sheetsDataValidationZhCN,
        sheetsFilterZhCN
      )
    },
    theme: defaultTheme,
    presets: [
      UniverSheetsCorePreset({
        container: container.value as HTMLElement
      }),
      UniverSheetsConditionalFormattingPreset(),
      UniverSheetsDataValidationPreset(),
      UniverSheetsFilterPreset()
    ]
  })

  univerInstance = univer
  univerAPIInstance = univerAPI

  // 创建初始工作簿
  const fWorkbook = univerAPIInstance.createWorkbook({
    id: 'universal-query-workbook',
    name: '综合查询结果'
  })
  fWorkbook.create('查询结果', 100, 20)

  console.log('Univer初始化成功')
})

// 组件卸载
onBeforeUnmount(() => {
  if (univerInstance) {
    univerInstance.dispose()
    univerInstance = null
  }
  if (univerAPIInstance) {
    univerAPIInstance.dispose()
    univerAPIInstance = null
  }
})
</script>

<style scoped>
.view-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.section-header {
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.section-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
  flex-wrap: wrap;
  align-items: center;
}

.action-button {
  padding: 4px 7px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
  font-size: 14px;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-button.fetch {
  background-color: #1890ff;
  color: white;
}

.action-button.save {
  background-color: #52c41a;
  color: white;
}

.action-button.export {
  background-color: #fa8c16;
  color: white;
}

.table-selector {
  margin-left: auto;
  margin-right: 10px;
}

.table-select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-width: 150px;
}

.rule-config-panel {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin-bottom: 20px;
  padding: 20px;
}

.rule-config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.rule-config-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.rules-container {
  margin-bottom: 20px;
}

.rule-item {
  margin-bottom: 12px;
  padding: 16px;
  background: #f8f9fb;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.rule-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.rule-select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.rule-input {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-width: 150px;
}

.logic-select {
  min-width: 80px;
}

.preset-templates {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.preset-templates h4 {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

.template-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.template-button {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.template-button:hover {
  background: #f0f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.table-container {
  flex: 1;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .table-selector {
    margin-left: 0;
    margin-right: 0;
  }

  .rule-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .rule-controls > * {
    width: 100% !important;
    min-width: auto !important;
  }
}
</style>
