# fillRedTextToIncomeSheet 函数使用说明

## 功能概述

`fillRedTextToIncomeSheet` 函数用于根据定位符列将"快速取数"sheet中的数据补全到"收入成本测算"sheet中，处理列名映射关系，并从上一行补全公式。

## 函数位置

文件路径：`src/scripts/fillCloseTamplate.ts`

## 函数签名

```typescript
export function fillRedTextToIncomeSheet(univerAPIInstance: FUniver): void
```

## 参数说明

- `univerAPIInstance`: Univer API实例，用于操作Excel工作簿

## 功能详细说明

### 1. 数据补全逻辑

函数通过以下逻辑进行数据补全：
- 比较"快速取数"sheet和"收入成本测算"sheet中的定位符列
- 找到在"收入成本测算"sheet中缺失的定位符值
- 将这些缺失的数据从"快速取数"sheet补全到"收入成本测算"sheet

### 2. 列名映射功能

函数内置了列名映射关系，处理两个sheet之间列名不一致的情况：

| 收入成本测算列名 | 快速取数列名 |
|------------------|--------------|
| 定位符 | 定位符 |
| 组织机构 | 利润中心组名称 |
| 项目名称 | 项目名称 |
| 项目状态 | 项目状态 |
| 利润中心编码 | 利润中心编码 |
| 项目编码 | 项目编码 |
| 结账预计收入 | 结账预计收入 |
| 结账预计成本 | 结账预计成本 |
| 收入合同编号 | 收入合同编号 |
| 本期毛利 | 本期毛利 |

### 3. 数据复制功能

- 根据列名映射关系自动匹配对应的列
- 只复制非空值，避免覆盖现有数据
- 智能处理列名不一致的情况

### 4. 公式补全功能

- 自动检测上一行中的公式（以"="开头的单元格值）
- 复制公式到新插入的行
- 自动调整公式中的行号引用（相对引用）
- 保持绝对引用（$符号标记的引用）不变
- 智能避免覆盖已有数据的单元格

## 使用示例

### 在Vue组件中使用

```typescript
import { fillRedTextToIncomeSheet } from '@/scripts/fillCloseTamplate';

// 在组件的方法中调用
const handleFillRedText = () => {
  if (univerAPIInstance) {
    fillRedTextToIncomeSheet(univerAPIInstance);
  } else {
    console.error('Univer API实例未初始化');
  }
};
```

### 在按钮点击事件中使用

```vue
<template>
  <el-button @click="handleFillRedText" type="primary">
    填充红色文本数据
  </el-button>
</template>

<script setup>
import { fillRedTextToIncomeSheet } from '@/scripts/fillCloseTamplate';

const handleFillRedText = () => {
  try {
    fillRedTextToIncomeSheet(univerAPIInstance);
    ElMessage.success('数据填充完成');
  } catch (error) {
    console.error('填充数据时出错:', error);
    ElMessage.error('数据填充失败');
  }
};
</script>
```

## 前置条件

1. 工作簿中必须存在"快速取数"sheet
2. 工作簿中必须存在"收入成本测算"sheet
3. 两个sheet都必须包含"定位符"列
4. Univer API实例必须已正确初始化

## 错误处理

函数包含以下错误检查：
- 检查sheet是否存在
- 检查定位符列是否存在
- 在控制台输出详细的错误信息和执行状态

## 注意事项

1. 函数会在"收入成本测算"sheet的末尾插入新行
2. 公式调整仅支持标准的Excel单元格引用格式（如A1, B2, $C$3）
3. 函数执行过程中会在控制台输出详细日志
4. 建议在执行前备份重要数据

## 相关函数

- `adjustFormulaForNewRow`: 调整公式中的行号引用的辅助函数
- `fillCloseTamplate`: 原有的模板填充函数
- `fillCloseTamplate2`: 原有的收入成本模板填充函数
