from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import duckdb
from datetime import datetime
import json

router = APIRouter()

# 数据库连接
DB_PATH = "financial_data.duckdb"

# 请求模型
class SalaryTaxFetchRequest(BaseModel):
    filters: Dict[str, Any]
    timestamp: str

class SalaryTaxSaveRequest(BaseModel):
    year: str
    taxDeclaration: List[List[Any]]
    taxCalculation: List[List[Any]]
    specialDeduction: List[List[Any]]
    remoteTax: List[List[Any]]
    idCard: List[List[Any]]
    projectMapping: List[List[Any]]
    subsidyPackage: List[List[Any]]
    bonus: List[List[Any]]
    outsourcingSalary: List[List[Any]]
    socialSecurityAndHousingFund: List[List[Any]]
    socialSecurityAndHousingFundAdjustment: List[List[Any]]
    timestamp: str

# 响应模型
class SalaryTaxFetchResponse(BaseModel):
    code: int
    message: str
    data: Dict[str, List[List[Any]]]
    timestamp: str

class SalaryTaxSaveResponse(BaseModel):
    code: int
    message: str
    timestamp: str

def init_salary_tax_tables():
    """初始化薪酬个税相关表"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        # 创建薪酬个税数据表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS salary_tax_data (
                id INTEGER PRIMARY KEY,
                year VARCHAR,
                table_name VARCHAR,
                data_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.close()
    except Exception as e:
        print(f"初始化薪酬个税表失败: {e}")

@router.post("/api/salary-tax/fetch-all", response_model=SalaryTaxFetchResponse)
async def fetch_all_salary_tax_data(request: SalaryTaxFetchRequest):
    """拉取所有薪酬个税数据"""
    try:
        # 初始化表
        init_salary_tax_tables()
        
        conn = duckdb.connect(DB_PATH)
        
        fiscal_year = request.filters.get('fiscalYear', '')
        if not fiscal_year:
            raise HTTPException(status_code=400, detail="缺少年份参数")
        
        # 查询指定年份的数据
        query = """
            SELECT table_name, data_json 
            FROM salary_tax_data 
            WHERE year = ? 
            ORDER BY updated_at DESC
        """
        
        result = conn.execute(query, [fiscal_year]).fetchall()
        
        # 构建返回数据
        data = {}
        table_names = [
            'taxDeclaration', 'taxCalculation', 'specialDeduction', 'remoteTax',
            'idCard', 'projectMapping', 'subsidyPackage', 'bonus',
            'outsourcingSalary', 'socialSecurityAndHousingFund', 
            'socialSecurityAndHousingFundAdjustment'
        ]
        
        # 初始化默认数据结构
        for table_name in table_names:
            data[table_name] = get_default_table_data(table_name)
        
        # 更新从数据库获取的数据
        for row in result:
            table_name, data_json = row
            if table_name in data and data_json:
                try:
                    data[table_name] = json.loads(data_json)
                except json.JSONDecodeError:
                    print(f"解析表 {table_name} 数据失败")
        
        conn.close()
        
        return SalaryTaxFetchResponse(
            code=200,
            message=f"成功获取 {fiscal_year} 年度薪酬个税数据",
            data=data,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"拉取数据失败: {str(e)}")

@router.post("/api/salary-tax/save-all", response_model=SalaryTaxSaveResponse)
async def save_all_salary_tax_data(request: SalaryTaxSaveRequest):
    """保存所有薪酬个税数据"""
    try:
        # 初始化表
        init_salary_tax_tables()
        
        conn = duckdb.connect(DB_PATH)
        
        # 准备要保存的数据
        tables_data = {
            'taxDeclaration': request.taxDeclaration,
            'taxCalculation': request.taxCalculation,
            'specialDeduction': request.specialDeduction,
            'remoteTax': request.remoteTax,
            'idCard': request.idCard,
            'projectMapping': request.projectMapping,
            'subsidyPackage': request.subsidyPackage,
            'bonus': request.bonus,
            'outsourcingSalary': request.outsourcingSalary,
            'socialSecurityAndHousingFund': request.socialSecurityAndHousingFund,
            'socialSecurityAndHousingFundAdjustment': request.socialSecurityAndHousingFundAdjustment
        }
        
        # 删除该年份的旧数据
        conn.execute("DELETE FROM salary_tax_data WHERE year = ?", [request.year])
        
        # 插入新数据
        insert_query = """
            INSERT INTO salary_tax_data (year, table_name, data_json, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        saved_count = 0
        for table_name, table_data in tables_data.items():
            if table_data and len(table_data) > 1:  # 确保有数据（除了表头）
                data_json = json.dumps(table_data, ensure_ascii=False)
                conn.execute(insert_query, [request.year, table_name, data_json])
                saved_count += 1
        
        conn.close()
        
        return SalaryTaxSaveResponse(
            code=200,
            message=f"成功保存 {saved_count} 个表格的 {request.year} 年度薪酬个税数据",
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存数据失败: {str(e)}")

def get_default_table_data(table_name: str) -> List[List[Any]]:
    """获取默认表格数据"""
    defaults = {
        'taxDeclaration': [
            ['工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
             '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
             '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
             '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
             '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
             '准予扣除的捐赠额', '减免税额', '备注']
        ],
        'taxCalculation': [
            ['月份', '算税地区','社保缴纳单位','公积金缴纳单位', '身份证号', '姓名', '成本所属项目','财务标准项目','财务标准单位', '薪酬类别',
             '本期收入', '基本养老保险费', '住房公积金', '基本医疗保险费',
             '失业保险费', '企业(职业)年金', '其它扣款', '调整收入', '调整扣除',
             '调整累计个税', '累计社保', '累计专项附加', '累计法定扣除',
             '累计调整扣除', '累计收入', '累计扣除', '累计应扣税款',
             '累计上次税款', '本次税款', '本次达到税率', '一次性年终奖校验','备注']
        ],
        'specialDeduction': [
            ['工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止',
             '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息',
             '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金']
        ],
        'remoteTax': [
            ['预留', '身份证号', '姓名', '一月', '二月', '三月', '四月', '五月', '六月',
             '七月', '八月', '九月', '十月', '十一月', '十二月']
        ],
        'idCard': [
            ['姓名', '身份证号','银行卡号','开户行名称','开户行编码','备注']
        ],
        'projectMapping': [
            ['人资项目名称', '标准项目名称', '标准项目编码','财务标准单位','财务标准单位编码']
        ],
        'subsidyPackage': [
            ['月份', '身份证号', '姓名', '成本所属项目', '财务标准项目', '工作餐补贴','交通补贴','通讯补贴','住房补贴','取证补贴','其他补贴','合计补贴','备注']
        ],
        'bonus': [
            ['缴纳月份', '算税地区',  '身份证号', '姓名', '成本所属项目', '财务标准项目', '薪酬类别', '原奖金金额','报税收入','税额','税率','备注']
        ],
        'outsourcingSalary': [
            ['月份','外包公司','姓名','身份证号','人资项目','财务标准项目','总金额','进项税','入账成本','薪酬类别','备注']
        ],
        'socialSecurityAndHousingFund': [
            ['月份','代缴单位','公积金金额','养老金额','医疗金额','补充医疗','失业金额','工伤金额','生育金额','年金','合计金额','备注']
        ],
        'socialSecurityAndHousingFundAdjustment': [
            ['月份','代缴单位','公积金金额','养老金额','医疗金额','补充医疗','失业金额','工伤金额','生育金额','年金','合计金额','备注']
        ]
    }
    
    return defaults.get(table_name, [[]])
