<template>
  <div class="vtable-edit-bug-test">
    <h1>VTable 编辑 Bug 测试</h1>
    
    <div class="test-info">
      <h2>测试说明</h2>
      <ul>
        <li>双击任意单元格进入编辑模式</li>
        <li>输入新值并按 Enter 确认</li>
        <li>观察数据是否出现在正确的位置</li>
        <li>查看控制台输出了解事件触发情况</li>
      </ul>
    </div>
    
    <div class="test-controls">
      <button @click="resetData" class="test-btn">重置测试数据</button>
      <button @click="logTableData" class="test-btn">输出当前数据</button>
      <button @click="addTestRow" class="test-btn">添加测试行</button>
    </div>
    
    <div class="current-data">
      <h3>当前数据状态</h3>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
    
    <div class="table-container">
      <VTableComponent
        ref="tableRef"
        :data="testData"
        :width="800"
        :height="400"
        :show-filter="false"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="false"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
      />
    </div>
    
    <div class="edit-log">
      <h3>编辑日志</h3>
      <div v-for="(log, index) in editLogs" :key="index" class="log-entry">
        <span class="log-time">{{ log.time }}</span>
        <span class="log-content">{{ log.content }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 表格引用
const tableRef = ref(null)

// 测试数据
const testData = ref([
  ['姓名', '年龄', '部门', '薪资', '备注'],
  ['张三', '25', '技术部', '8000', '前端开发'],
  ['李四', '30', '产品部', '9000', '产品经理'],
  ['王五', '28', '设计部', '7500', 'UI设计师'],
  ['赵六', '32', '技术部', '12000', '后端开发'],
  ['钱七', '26', '市场部', '6500', '市场专员']
])

// 编辑日志
const editLogs = ref([])

// 添加日志
function addLog(content) {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  editLogs.value.unshift({
    time,
    content
  })
  // 只保留最近20条日志
  if (editLogs.value.length > 20) {
    editLogs.value = editLogs.value.slice(0, 20)
  }
}

// 数据变化处理
const handleDataChange = (newData) => {
  console.log('数据变化事件触发:', newData)
  addLog(`数据变化: 共 ${newData.length - 1} 行数据`)
  testData.value = newData
}

// 单元格编辑处理
const handleCellEdit = (editInfo) => {
  console.log('单元格编辑事件:', editInfo)
  addLog(`编辑单元格: 行${editInfo.row}, 列${editInfo.col}, ${editInfo.oldValue} → ${editInfo.newValue}`)
}

// 重置测试数据
const resetData = () => {
  testData.value = [
    ['姓名', '年龄', '部门', '薪资', '备注'],
    ['张三', '25', '技术部', '8000', '前端开发'],
    ['李四', '30', '产品部', '9000', '产品经理'],
    ['王五', '28', '设计部', '7500', 'UI设计师'],
    ['赵六', '32', '技术部', '12000', '后端开发'],
    ['钱七', '26', '市场部', '6500', '市场专员']
  ]
  addLog('重置测试数据')
}

// 输出当前数据
const logTableData = () => {
  console.log('当前表格数据:', testData.value)
  addLog('已输出当前数据到控制台')
}

// 添加测试行
const addTestRow = () => {
  const newRow = ['新员工', '24', '测试部', '7000', '测试工程师']
  testData.value.push(newRow)
  addLog('添加了新的测试行')
}

// 初始化日志
addLog('页面初始化完成')
</script>

<style scoped>
.vtable-edit-bug-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-info h2 {
  margin-top: 0;
  color: #333;
}

.test-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-info li {
  margin: 5px 0;
  color: #666;
}

.test-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.test-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-btn:hover {
  background: #0056b3;
}

.current-data {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  max-height: 200px;
  overflow-y: auto;
}

.current-data h3 {
  margin-top: 0;
  color: #333;
}

.current-data pre {
  font-size: 12px;
  color: #666;
  margin: 0;
  white-space: pre-wrap;
}

.table-container {
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.edit-log {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.edit-log h3 {
  margin-top: 0;
  color: #333;
}

.log-entry {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  font-family: monospace;
  min-width: 60px;
}

.log-content {
  color: #333;
  flex: 1;
}
</style>
