<template>
  <div class="demo-container">
    <h1>资金流动图表演示</h1>
    
    <!-- 图表切换按钮 -->
    <div class="chart-controls">
      <el-radio-group v-model="chartType" @change="switchChart" size="large">
        <el-radio-button label="pie">🥧 饼图分析</el-radio-button>
        <el-radio-button label="waterfall">📊 瀑布图</el-radio-button>
        <el-radio-button label="sankey">🌊 桑基图</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 图表容器 -->
    <div class="chart-section">
      <div class="chart-container">
        <div id="fundDistributionChart" class="chart" v-show="chartType === 'pie'"></div>
        <div id="waterfallChart" class="chart" v-show="chartType === 'waterfall'"></div>
        <div id="sankeyChart" class="chart" v-show="chartType === 'sankey'"></div>
      </div>
    </div>

    <!-- 数据说明 -->
    <div class="data-info">
      <h3>📈 图表说明</h3>
      <ul>
        <li><strong>饼图分析：</strong>直观展示各项资金流入流出的占比，绿色表示流入，红色表示流出</li>
        <li><strong>瀑布图：</strong>展示资金从期初到期末的变化过程，清晰显示每项收支对总额的影响</li>
        <li><strong>桑基图：</strong>以流动的方式展示资金在不同项目间的流转关系</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";

// 页面状态
const chartType = ref('pie');

// 模拟资金流动数据
const fundDistributionData = ref([
  { value: 1500000, name: '项目收入' },
  { value: 800000, name: '投资收益' },
  { value: 300000, name: '其他收入' },
  { value: -600000, name: '运营成本' },
  { value: -400000, name: '人员工资' },
  { value: -200000, name: '设备采购' },
  { value: -150000, name: '税费支出' },
  { value: -100000, name: '其他支出' }
]);

// 格式化数字
function formatNumber(num) {
  return new Intl.NumberFormat("zh-CN").format(num);
}

// 图表切换函数
function switchChart() {
  setTimeout(() => {
    switch (chartType.value) {
      case 'pie':
        initFundDistributionChart();
        break;
      case 'waterfall':
        initWaterfallChart();
        break;
      case 'sankey':
        initSankeyChart();
        break;
    }
  }, 100);
}

// 初始化资金流动饼图
function initFundDistributionChart() {
  const chartDom = document.getElementById("fundDistributionChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 处理资金流动数据，区分流入和流出
  let processedData = [];
  let inflowTotal = 0;
  let outflowTotal = 0;
  
  fundDistributionData.value.forEach(item => {
    if (item.value > 0) {
      processedData.push({
        ...item,
        name: `${item.name} (流入)`,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#67C23A' },
            { offset: 0.5, color: '#85CE61' },
            { offset: 1, color: '#95D475' }
          ])
        }
      });
      inflowTotal += item.value;
    } else if (item.value < 0) {
      processedData.push({
        ...item,
        value: Math.abs(item.value),
        name: `${item.name} (流出)`,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#F56C6C' },
            { offset: 0.5, color: '#F78989' },
            { offset: 1, color: '#F9A7A7' }
          ])
        }
      });
      outflowTotal += Math.abs(item.value);
    }
  });

  const option = {
    title: [
      {
        text: '资金流动分析',
        left: 'center',
        top: '5%',
        textStyle: {
          fontSize: 20,
          fontWeight: 'bold',
          color: '#2c3e50'
        }
      },
      {
        text: `净流入: ¥${formatNumber(inflowTotal - outflowTotal)}`,
        left: '15%',
        top: '15%',
        textStyle: {
          fontSize: 14,
          color: inflowTotal > outflowTotal ? '#67C23A' : '#F56C6C',
          fontWeight: 'bold'
        }
      },
      {
        text: `流入总额: ¥${formatNumber(inflowTotal)}`,
        right: '15%',
        top: '15%',
        textStyle: {
          fontSize: 12,
          color: '#67C23A'
        }
      },
      {
        text: `流出总额: ¥${formatNumber(outflowTotal)}`,
        right: '15%',
        top: '20%',
        textStyle: {
          fontSize: 12,
          color: '#F56C6C'
        }
      }
    ],
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      formatter: function(params) {
        const isInflow = params.name.includes('流入');
        const arrow = isInflow ? '↗️' : '↘️';
        const type = isInflow ? '资金流入' : '资金流出';
        return `${arrow} ${type}<br/>${params.name}<br/>金额: ¥${formatNumber(params.value)}<br/>占比: ${params.percent}%`;
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: '5%',
      top: '30%',
      bottom: '10%',
      data: processedData.map(item => item.name),
      textStyle: {
        color: '#333',
        fontSize: 12
      }
    },
    series: [
      {
        name: '资金流动',
        type: 'pie',
        radius: ['35%', '65%'],
        center: ['40%', '60%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 3,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.2)'
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 11,
          fontWeight: 'bold',
          formatter: function(params) {
            const isInflow = params.name.includes('流入');
            const arrow = isInflow ? '↗️' : '↘️';
            return `${arrow} ${params.name.replace(/ \(流[入出]\)/, '')}\n¥${formatNumber(params.value)}`;
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 0, 0, 0.4)'
          },
          scaleSize: 5
        },
        data: processedData,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200;
        }
      }
    ],
    graphic: [
      {
        type: 'text',
        left: 'center',
        bottom: '5%',
        style: {
          text: '💡 提示：绿色表示资金流入，红色表示资金流出',
          fontSize: 12,
          fill: '#999'
        }
      }
    ]
  };

  myChart.setOption(option);
  
  // 添加点击事件
  myChart.on('click', function(params) {
    if (params.data && params.data.value > 0) {
      ElMessage.info(`${params.name}: ¥${formatNumber(params.data.value)}`);
    }
  });
  
  window.addEventListener("resize", () => myChart.resize());
}

// 初始化瀑布图
function initWaterfallChart() {
  const chartDom = document.getElementById("waterfallChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 处理瀑布图数据
  let categories = ['期初余额'];
  let data = [{ value: 0, itemStyle: { color: '#91cc75' } }]; // 期初余额
  let total = 0;

  fundDistributionData.value.forEach(item => {
    categories.push(item.name);
    total += item.value;

    if (item.value > 0) {
      // 流入 - 绿色
      data.push({
        value: item.value,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#67C23A' },
            { offset: 1, color: '#85CE61' }
          ])
        }
      });
    } else {
      // 流出 - 红色
      data.push({
        value: item.value,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#F56C6C' },
            { offset: 1, color: '#F78989' }
          ])
        }
      });
    }
  });

  categories.push('期末余额');
  data.push({
    value: total,
    itemStyle: {
      color: total >= 0 ? '#409EFF' : '#E6A23C'
    }
  });

  const option = {
    title: {
      text: '资金流动瀑布图',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      textStyle: {
        color: '#fff'
      },
      formatter: function(params) {
        const param = params[0];
        const value = param.value;
        const isInflow = value > 0;
        const arrow = isInflow ? '↗️' : '↘️';
        const type = isInflow ? '流入' : '流出';

        if (param.name === '期初余额' || param.name === '期末余额') {
          return `${param.name}<br/>金额: ¥${formatNumber(Math.abs(value))}`;
        }

        return `${arrow} ${type}<br/>${param.name}<br/>金额: ¥${formatNumber(Math.abs(value))}`;
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        rotate: 45,
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '金额（元）',
      axisLabel: {
        formatter: function(value) {
          return '¥' + formatNumber(value);
        }
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '资金流动',
        type: 'bar',
        barWidth: '60%',
        data: data,
        label: {
          show: true,
          position: 'top',
          formatter: function(params) {
            const value = params.value;
            const sign = value >= 0 ? '+' : '';
            return sign + formatNumber(value);
          },
          fontSize: 10,
          fontWeight: 'bold'
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        animationDelay: function (idx) {
          return idx * 100;
        }
      }
    ],
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 50;
    }
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 初始化桑基图
function initSankeyChart() {
  const chartDom = document.getElementById("sankeyChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 构建桑基图数据
  const nodes = [
    { name: '资金池', itemStyle: { color: '#409EFF' } }
  ];
  const links = [];

  fundDistributionData.value.forEach(item => {
    if (item.value !== 0) {
      nodes.push({
        name: item.name,
        itemStyle: {
          color: item.value > 0 ? '#67C23A' : '#F56C6C'
        }
      });

      if (item.value > 0) {
        // 流入：从来源到资金池
        links.push({
          source: item.name,
          target: '资金池',
          value: item.value,
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#67C23A' },
              { offset: 1, color: '#85CE61' }
            ])
          }
        });
      } else {
        // 流出：从资金池到去向
        links.push({
          source: '资金池',
          target: item.name,
          value: Math.abs(item.value),
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#F56C6C' },
              { offset: 1, color: '#F78989' }
            ])
          }
        });
      }
    }
  });

  const option = {
    title: {
      text: '资金流动桑基图',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      textStyle: {
        color: '#fff'
      },
      formatter: function(params) {
        if (params.dataType === 'edge') {
          const isInflow = params.data.source !== '资金池';
          const arrow = isInflow ? '→' : '←';
          const type = isInflow ? '流入' : '流出';
          return `${type} ${arrow}<br/>${params.data.source} → ${params.data.target}<br/>金额: ¥${formatNumber(params.data.value)}`;
        } else {
          return `${params.name}<br/>节点`;
        }
      }
    },
    series: [
      {
        type: 'sankey',
        layout: 'none',
        emphasis: {
          focus: 'adjacency'
        },
        nodeAlign: 'left',
        nodeGap: 20,
        nodeWidth: 30,
        layoutIterations: 0,
        data: nodes,
        links: links,
        lineStyle: {
          opacity: 0.8,
          curveness: 0.3
        },
        label: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
    ]
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 页面加载时初始化
onMounted(() => {
  switchChart();
});
</script>

<style scoped>
.demo-container {
  width: 100%;
  height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-y: auto;
}

h1 {
  text-align: center;
  color: white;
  font-size: 32px;
  margin-bottom: 32px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chart-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.chart-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.chart-container {
  width: 100%;
  height: 500px;
}

.chart {
  width: 100%;
  height: 100%;
}

.data-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.data-info h3 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.data-info ul {
  list-style: none;
  padding: 0;
}

.data-info li {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}
</style>
