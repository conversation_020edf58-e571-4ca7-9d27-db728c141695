# 速填模板功能说明

## 概述

速填模板是一个新增的功能模块，类似于快速查询，提供四种不同的数据录入模板，每个模板都包含获取模板、计算补充、推送数据三个核心功能。

## 功能特性

### 1. 卡片式选择界面
- 采用与快速查询相同的卡片选择设计
- 四个功能卡片：发票速录、物资结算、物资出库、派遣工资
- 每个卡片都有对应的图标和描述

### 2. 统一的操作界面
每个速填模板都包含：
- **返回按钮**：返回卡片选择界面
- **功能按钮区域**：
  - 获取模板：获取预定义的数据模板
  - 计算补充：自动计算相关字段
  - 推送数据：将数据推送到后台系统
- **VTable展示区域**：支持编辑、复制粘贴、Excel导入等功能

## 四个模板详情

### 1. 发票速录 (InvoiceEntryComponent)
**用途**：快速录入发票信息

**字段包含**：
- 发票号码、发票日期、供应商名称、税号
- 商品名称、数量、单价、金额
- 税率、税额、价税合计、备注

**计算功能**：
- 自动计算金额 = 数量 × 单价
- 自动计算税额 = 金额 × 税率
- 自动计算价税合计 = 金额 + 税额

### 2. 物资结算 (MaterialSettlementComponent)
**用途**：物资采购结算处理

**字段包含**：
- 结算单号、结算日期、供应商名称
- 物资编码、物资名称、规格型号、单位
- 数量、单价、金额、已付金额、未付金额、备注

**计算功能**：
- 自动计算金额 = 数量 × 单价
- 自动计算未付金额 = 金额 - 已付金额

### 3. 物资出库 (MaterialOutboundComponent)
**用途**：物资出库单据处理

**字段包含**：
- 出库单号、出库日期、领用部门、领用人
- 物资编码、物资名称、规格型号、单位
- 出库数量、单价、出库金额、库存数量、用途、备注

**计算功能**：
- 自动计算出库金额 = 出库数量 × 单价
- 自动更新库存数量 = 原库存 - 出库数量

### 4. 派遣工资 (DispatchSalaryComponent)
**用途**：派遣人员工资处理

**字段包含**：
- 工号、姓名、部门、岗位、工作天数
- 基本工资、绩效工资、加班费、津贴补助
- 应发工资、社保个人、公积金个人、个人所得税、其他扣款、实发工资、备注

**计算功能**：
- 自动计算应发工资 = 基本工资 + 绩效工资 + 加班费 + 津贴补助
- 自动计算个人所得税（简化计算）
- 自动计算实发工资 = 应发工资 - 各项扣款

## 技术实现

### 路由配置
```javascript
{
  path: '/quick-fill',
  name: 'quick-fill',
  component: QuickFillView,
  meta: {
    title: '速填模板',
    icon: 'EditPen'
  }
}
```

### 组件结构
```
src/views/QuickFillView.vue          # 主视图，包含卡片选择
src/components/InvoiceEntryComponent.vue      # 发票速录组件
src/components/MaterialSettlementComponent.vue # 物资结算组件
src/components/MaterialOutboundComponent.vue   # 物资出库组件
src/components/DispatchSalaryComponent.vue     # 派遣工资组件
```

### VTable集成
- 使用现有的VTableComponent
- 支持编辑、复制粘贴、Excel导入
- 响应式表格尺寸
- 数据变化监听

## 使用方法

1. 在侧边栏导航中点击"速填模板"
2. 在卡片界面选择需要的模板类型
3. 点击"获取模板"按钮加载预定义模板
4. 在表格中录入或导入数据
5. 点击"计算补充"自动计算相关字段
6. 点击"推送数据"将数据提交到后台

## 扩展性

该模块设计具有良好的扩展性：
- 可以轻松添加新的模板类型
- 每个模板的字段和计算逻辑都是独立的
- 支持自定义模板数据和计算规则
- 可以集成更复杂的后台API

## 注意事项

- 所有计算功能目前使用模拟逻辑，实际部署时需要根据业务需求调整
- 推送数据功能需要配置相应的后台API端点
- 建议在生产环境中添加数据验证和错误处理机制
