<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>薪酬税务视图复制粘贴修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .problem-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin-bottom: 20px;
        }
        .fix-section {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
            margin-bottom: 20px;
        }
        .test-section {
            background: #d4edda;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin-bottom: 20px;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 8px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #495057;
            margin-top: 25px;
        }
        h3 {
            color: #6c757d;
            margin-top: 20px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>薪酬税务视图复制粘贴修复验证</h1>
        
        <div class="problem-section">
            <h2>🐛 问题描述</h2>
            <p><strong>用户反馈：</strong>"这个视图里面的vtable复制粘贴存在跨行情况，测试vtable又没有问题"</p>
            
            <h3>具体问题</h3>
            <ul>
                <li>在 <code>SalaryTaxView.vue</code> 中，特别是"算税底稿"表格</li>
                <li>包含字段：<span class="highlight">失业保险费、企业(职业)年金、其它扣款、调整收入、调整扣除</span></li>
                <li>复制单个值粘贴时出现跨行现象</li>
                <li>而独立的 VTable 测试页面没有问题</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>🔧 问题根因分析</h2>
            
            <h3>SalaryTaxView 的特殊架构</h3>
            <div class="code-block">
&lt;!-- 多个 VTable 组件使用 v-show 切换 --&gt;
&lt;VTableComponent v-show="activeTabIndex === 0" .../&gt;
&lt;VTableComponent v-show="activeTabIndex === 1" .../&gt;
&lt;VTableComponent v-show="activeTabIndex === 2" .../&gt;
...共11个表格组件
            </div>
            
            <h3>问题原因</h3>
            <ul>
                <li><strong>所有组件同时存在于 DOM 中</strong> - 只是通过 v-show 控制显示/隐藏</li>
                <li><strong>键盘事件监听器冲突</strong> - 多个组件都监听 Ctrl+V 事件</li>
                <li><strong>粘贴事件重复触发</strong> - 隐藏的组件也会响应粘贴操作</li>
                <li><strong>状态混乱</strong> - 多个组件实例之间可能存在状态干扰</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>🛠️ 修复方案</h2>
            
            <h3>1. 添加组件可见性检查</h3>
            <div class="code-block">
const isComponentVisible = () => {
  if (!vtableInstance) return false;
  
  const container = vtableInstance.getContainer();
  if (!container) return false;
  
  const rect = container.getBoundingClientRect();
  const style = window.getComputedStyle(container);
  
  return (
    container.offsetParent !== null &&
    style.display !== 'none' &&
    style.visibility !== 'hidden' &&
    rect.width > 0 &&
    rect.height > 0
  );
};
            </div>
            
            <h3>2. 修改键盘事件处理</h3>
            <div class="code-block">
vtableInstance.on('keydown', (args) => {
  if (args.event && args.event.ctrlKey && args.event.key === 'v') {
    // 只有可见的组件才处理粘贴
    if (!isComponentVisible()) {
      console.log('VTable组件不可见，跳过粘贴处理');
      return;
    }
    
    args.event.preventDefault();
    handlePaste();
  }
});
            </div>
            
            <h3>3. 修改粘贴处理方法</h3>
            <div class="code-block">
const handlePaste = async () => {
  // 再次检查可见性
  if (!isComponentVisible()) {
    return;
  }
  
  // 执行粘贴逻辑...
};
            </div>
        </div>

        <div class="test-section">
            <h2>✅ 测试验证</h2>
            
            <h3>测试页面</h3>
            <p>创建了专门的测试页面来验证修复效果：</p>
            <ul>
                <li><strong>通用测试</strong>: <code>/vtable-paste-fix-test</code></li>
                <li><strong>薪酬税务专项测试</strong>: <code>/salary-tax-paste-test</code></li>
            </ul>
            
            <div class="test-steps">
                <h4>测试步骤：</h4>
                <ol>
                    <li>访问薪酬税务测试页面</li>
                    <li>切换到不同的标签页（个税申报表、算税底稿、包干费）</li>
                    <li>在当前可见表格中选中一个单元格</li>
                    <li>复制一个单个值（如："测试值"）</li>
                    <li>使用 Ctrl+V 粘贴</li>
                    <li>验证值只出现在当前选中的单元格中</li>
                    <li>切换到其他标签页，重复测试</li>
                    <li>检查控制台日志，确认只有可见组件处理了粘贴事件</li>
                </ol>
            </div>
            
            <h3>预期结果</h3>
            <ul>
                <li>✅ 单个值粘贴只影响当前可见表格的选中单元格</li>
                <li>✅ 隐藏的表格组件不会响应粘贴事件</li>
                <li>✅ 控制台显示清晰的调试信息</li>
                <li>✅ 多值粘贴功能保持正常</li>
                <li>✅ 表格切换功能不受影响</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🚀 快速测试</h2>
            <p>点击下面的链接直接访问测试页面：</p>
            <a href="/vtable-paste-fix-test" class="btn">通用粘贴修复测试</a>
            <a href="/salary-tax-paste-test" class="btn">薪酬税务粘贴测试</a>
            <a href="/salary-tax" class="btn success">薪酬税务主页面</a>
            
            <h3>测试重点</h3>
            <div class="test-steps">
                <p><strong>特别关注以下场景：</strong></p>
                <ul>
                    <li>在"算税底稿"标签页中测试包含特殊字段的行</li>
                    <li>快速切换标签页后立即粘贴</li>
                    <li>同时打开多个浏览器标签页测试</li>
                    <li>复制包含制表符和换行符的复杂数据</li>
                </ul>
            </div>
        </div>

        <div class="fix-section">
            <h2>📋 修复文件清单</h2>
            <ul>
                <li><code>src/components/VTableComponent.vue</code> - 主要修复文件</li>
                <li><code>src/views/VTablePasteFixTest.vue</code> - 通用测试页面</li>
                <li><code>src/views/SalaryTaxPasteTest.vue</code> - 薪酬税务专项测试页面</li>
                <li><code>src/router/index.js</code> - 添加测试页面路由</li>
                <li><code>docs/VTable复制粘贴修复说明.md</code> - 详细修复文档</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🎯 验收标准</h2>
            <p><strong>修复成功的标准：</strong></p>
            <ol>
                <li>在 SalaryTaxView 中，单个值粘贴只影响当前可见表格的选中单元格</li>
                <li>切换标签页后，粘贴功能在新的可见表格中正常工作</li>
                <li>隐藏的表格组件不会干扰粘贴操作</li>
                <li>多值粘贴（如从 Excel 复制的表格数据）功能保持正常</li>
                <li>控制台输出清晰的调试信息，便于问题排查</li>
                <li>表格的其他功能（编辑、选择、滚动等）不受影响</li>
            </ol>
        </div>
    </div>
</body>
</html>
