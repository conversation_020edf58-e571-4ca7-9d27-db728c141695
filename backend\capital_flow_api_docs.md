# 资金流动分析API文档

## API端点
```
POST /api/capital-flow
```

## 请求参数

### 请求体 (JSON)
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-12-31"
}
```

### 参数说明
- `start_date`: 开始日期，格式为 YYYY-MM-DD
- `end_date`: 结束日期，格式为 YYYY-MM-DD

## 响应数据结构

### 响应体 (JSON)
```json
{
  "fund_status_data": [
    ["项目", "上期", "可用存款", "应付票据", "应付保理", "短期借款", "净存量"],
    ["上期", "¥5,200,000", "¥3,800,000", "¥800,000", "¥400,000", "¥200,000", "¥2,400,000"],
    ["当前", "¥5,800,000", "¥4,200,000", "¥900,000", "¥500,000", "¥300,000", "¥2,500,000"],
    ["变化", "+¥600,000", "+¥400,000", "+¥100,000", "+¥100,000", "+¥100,000", "+¥100,000"]
  ],
  "receipt_data": [
    ["收款类型", "总收款"],
    ["货币收款", "¥3,200,000"],
    ["抵房收款", "¥800,000"],
    ["票据收款", "¥600,000"],
    ["代付收款", "¥400,000"],
    ["其他收款", "¥300,000"],
    ["合计", "¥5,300,000"]
  ],
  "payment_pivot_data": [
    ["付款类型", "分包", "劳务", "材料", "合计"],
    ["总付款", "¥2,800,000", "¥1,200,000", "¥1,500,000", "¥5,500,000"],
    ["现金", "¥800,000", "¥400,000", "¥300,000", "¥1,500,000"],
    ["保理", "¥1,200,000", "¥500,000", "¥600,000", "¥2,300,000"],
    ["代付付款", "¥600,000", "¥200,000", "¥400,000", "¥1,200,000"],
    ["其他", "¥200,000", "¥100,000", "¥200,000", "¥500,000"]
  ],
  "pending_payment_data": [
    [
      "单据编号",
      "供应商名称",
      "付款类型",
      "申请金额",
      "审批状态",
      "申请日期",
      "预计付款日期",
      "项目名称"
    ],
    [
      "PD20240515001",
      "北京建筑公司",
      "分包付款",
      1200000,
      "审批中",
      "2024-05-15",
      "2024-05-20",
      "智慧城市项目"
    ]
    // ... 更多数据行
  ],
  "fund_distribution_data": [
    {"value": 2800000, "name": "分包付款"},
    {"value": 1200000, "name": "劳务付款"},
    {"value": 1500000, "name": "材料付款"},
    {"value": 800000, "name": "设备付款"},
    {"value": 500000, "name": "其他付款"}
  ]
}
```

## 数据字段说明

### 1. fund_status_data (资金情况数据)
二维数组格式，用于VTable组件展示：
- 第一行：列标题
- 后续行：数据行，包含上期、当前、变化三行数据
- 列包含：项目、上期、可用存款、应付票据、应付保理、短期借款、净存量

### 2. receipt_data (收款情况数据)
二维数组格式：
- 第一行：["收款类型", "总收款"]
- 数据行：各种收款类型及对应金额
- 包含：货币收款、抵房收款、票据收款、代付收款、其他收款、合计

### 3. payment_pivot_data (付款情况数据)
二维数组格式，pivot表格：
- 第一行：["付款类型", "分包", "劳务", "材料", "合计"]
- 数据行：按付款方式分类的金额统计
- 行包含：总付款、现金、保理、代付付款、其他

### 4. pending_payment_data (在途付款单据数据)
二维数组格式：
- 第一行：列标题
- 数据行：在途付款单据明细
- 列包含：单据编号、供应商名称、付款类型、申请金额、审批状态、申请日期、预计付款日期、项目名称

### 5. fund_distribution_data (资金分布数据)
对象数组格式，用于ECharts饼图：
- 每个对象包含 value (数值) 和 name (名称) 字段
- 用于展示不同付款类型的资金分布

## 数据库查询建议

### 资金情况查询
```sql
-- 查询指定时间段的资金情况
SELECT 
  SUM(available_deposit) as available_deposit,
  SUM(payable_notes) as payable_notes,
  SUM(payable_factoring) as payable_factoring,
  SUM(short_term_loans) as short_term_loans,
  SUM(net_inventory) as net_inventory
FROM fund_status 
WHERE date_range BETWEEN start_date AND end_date;
```

### 收款情况查询
```sql
-- 查询收款情况统计
SELECT 
  receipt_type,
  SUM(amount) as total_amount
FROM receipt_ledger 
WHERE posting_date BETWEEN start_date AND end_date
GROUP BY receipt_type;
```

### 付款情况查询
```sql
-- 查询付款情况pivot统计
SELECT 
  payment_method,
  SUM(CASE WHEN category = '分包' THEN amount ELSE 0 END) as subcontract,
  SUM(CASE WHEN category = '劳务' THEN amount ELSE 0 END) as labor,
  SUM(CASE WHEN category = '材料' THEN amount ELSE 0 END) as material,
  SUM(amount) as total
FROM payment_ledger 
WHERE posting_date BETWEEN start_date AND end_date
GROUP BY payment_method;
```

### 在途付款查询
```sql
-- 查询在途付款单据
SELECT 
  document_number,
  supplier_name,
  payment_type,
  application_amount,
  approval_status,
  application_date,
  expected_payment_date,
  project_name
FROM pending_payments 
WHERE application_date BETWEEN start_date AND end_date
AND approval_status IN ('审批中', '已审批', '待审批');
```

## 错误处理

### 错误响应格式
```json
{
  "detail": "错误信息描述"
}
```

### 常见错误码
- 400: 请求参数错误
- 500: 服务器内部错误

## 使用示例

### JavaScript/Vue.js
```javascript
async function fetchCapitalFlowData(startDate, endDate) {
  try {
    const response = await fetch('/api/capital-flow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        start_date: startDate,
        end_date: endDate
      })
    });
    
    if (!response.ok) {
      throw new Error('网络请求失败');
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取资金流动数据失败:', error);
    throw error;
  }
}
```

### Python
```python
import requests
import json

def get_capital_flow_data(start_date, end_date):
    url = "http://localhost:8000/api/capital-flow"
    payload = {
        "start_date": start_date,
        "end_date": end_date
    }
    
    response = requests.post(url, json=payload)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"API请求失败: {response.status_code}")
```
