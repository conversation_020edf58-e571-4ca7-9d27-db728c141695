# 综合查询视图 (UniversalQueryView)

## 概述

综合查询视图是一个基于Univer电子表格的高级查询工具，允许用户通过可视化的规则配置界面快速构建复杂的数据库查询，并在电子表格中查看和编辑结果。

## 主要特性

### 🔍 动态查询规则配置
- **可视化规则构建**: 通过下拉菜单选择表、字段、操作符和值
- **多条件组合**: 支持AND/OR逻辑关系组合多个查询条件
- **丰富的操作符**: 支持等于、包含、大于、小于、为空等多种查询操作
- **实时验证**: 自动验证查询规则的完整性和有效性

### 📊 Univer电子表格集成
- **实时数据展示**: 查询结果直接加载到Univer电子表格中
- **在线编辑**: 支持在电子表格中直接编辑查询结果
- **格式化显示**: 自动设置表头样式和列宽
- **数据导出**: 支持导出为Excel等格式

### 🎯 预设查询模板
- **快速开始**: 提供常用查询场景的预设模板
- **一键加载**: 点击模板按钮即可快速配置查询规则
- **可扩展**: 支持添加自定义查询模板

### 🔄 智能数据处理
- **多表支持**: 支持查询多个数据表
- **容错机制**: API失败时自动切换到模拟数据
- **性能优化**: 查询结果限制在1000条以内，确保响应速度

## 使用方法

### 1. 访问综合查询
在左侧导航菜单中点击"综合查询"进入功能页面。

### 2. 配置查询规则
1. 点击"添加规则"按钮添加新的查询条件
2. 依次选择：
   - **数据表**: 选择要查询的数据表（如科目对照、异常数据等）
   - **字段**: 选择要查询的字段
   - **操作符**: 选择查询操作（等于、包含、大于等）
   - **值**: 输入查询值
   - **逻辑关系**: 选择与下一个条件的逻辑关系（AND/OR）

### 3. 使用预设模板
点击预设模板按钮可以快速加载常用的查询配置：
- **科目对照查询**: 查询科目分类信息
- **合同金额查询**: 查询大额合同信息
- **异常数据查询**: 查询未处理的异常数据

### 4. 执行查询
配置完成后点击"执行查询"按钮，系统将：
1. 验证查询规则的完整性
2. 构建并执行SQL查询
3. 将结果加载到Univer电子表格中
4. 显示查询状态和记录数量

### 5. 查看和编辑结果
- 查询结果将在Univer电子表格中显示
- 支持在表格中直接编辑数据
- 可以使用电子表格的所有功能（排序、筛选、公式等）

## 支持的数据表

当前版本支持以下数据表的查询：

| 表名 | 中文名称 | 主要字段 |
|------|----------|----------|
| subject_mapping | 科目对照 | 总账科目长文本、科目分类1、科目分类2、科目方向 |
| exception_data | 异常数据 | 异常类型、异常描述、发生日期、处理状态 |
| integrated_contract | 一体化合同台账 | 合同编号、合同名称、客商名称、合同金额、合同类型 |
| master_data | 主数据 | 项目编码、利润中心、核算组织、利润中心描述 |
| payment_ledger | 付款台账 | 付款日期、付款金额、收款方、付款方式 |
| guarantee_ledger | 担保台账 | 担保类型、担保金额、担保期限、担保状态 |
| internal_bank | 内部银行 | 账户名称、账户余额、交易类型、交易金额 |
| internal_reconciliation | 内部对账 | 对账日期、对账金额、对账状态、差异说明 |

## 查询操作符说明

| 操作符 | 说明 | 示例 |
|--------|------|------|
| 等于 | 精确匹配 | 状态 = "已完成" |
| 包含 | 模糊匹配 | 项目名称包含"建设" |
| 大于 | 数值比较 | 金额 > 100000 |
| 小于 | 数值比较 | 金额 < 50000 |
| 大于等于 | 数值比较 | 金额 >= 100000 |
| 小于等于 | 数值比较 | 金额 <= 50000 |
| 不等于 | 排除匹配 | 状态 != "已取消" |
| 为空 | 空值检查 | 备注为空 |
| 不为空 | 非空检查 | 联系人不为空 |

## 技术实现

### 前端技术栈
- **Vue 3**: 使用Composition API构建响应式界面
- **Element Plus**: 提供UI组件和交互元素
- **Univer**: 集成电子表格功能
- **Axios**: 处理HTTP请求

### 后端API
- **FastAPI**: 提供RESTful API接口
- **DuckDB**: 高性能数据库查询
- **Pydantic**: 数据验证和序列化

### 关键组件
- `UniversalQueryView.vue`: 主视图组件
- `/api/universal-query`: 后端查询API接口

## 扩展性

### 添加新的数据表
1. 在`availableTables`中添加新表配置
2. 在`tableFields`中定义表字段
3. 在后端API的`table_mapping`和`field_mapping`中添加映射

### 添加新的查询模板
在`queryTemplates`数组中添加新的模板配置：

```javascript
{
  name: '模板名称',
  rules: [
    {
      id: Date.now(),
      table: '表名',
      field: '字段名',
      operator: '操作符',
      value: '默认值',
      logic: 'and'
    }
  ]
}
```

### 自定义操作符
在`build_condition`函数中添加新的操作符处理逻辑。

## 注意事项

1. **性能考虑**: 查询结果限制在1000条以内，避免大数据量影响性能
2. **数据安全**: 所有查询都经过参数化处理，防止SQL注入
3. **容错处理**: API失败时会自动加载模拟数据，确保用户体验
4. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验

## 故障排除

### 常见问题

**Q: 查询结果为空怎么办？**
A: 检查查询条件是否正确，尝试放宽查询条件或使用预设模板。

**Q: Univer电子表格无法显示？**
A: 检查浏览器控制台是否有错误信息，确保网络连接正常。

**Q: 查询速度很慢？**
A: 尝试添加更具体的查询条件，减少结果集大小。

**Q: 无法添加查询规则？**
A: 检查是否已达到规则数量限制，或刷新页面重试。

## 更新日志

### v1.0.0 (2024-07-05)
- 初始版本发布
- 支持8个主要数据表的查询
- 集成Univer电子表格
- 提供3个预设查询模板
- 支持9种查询操作符
