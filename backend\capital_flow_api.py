from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from datetime import date
from typing import List, Dict, Any

router = APIRouter()

class CapitalFlowRequest(BaseModel):
    start_date: date
    end_date: date

class CapitalFlowResponse(BaseModel):
    fund_status_data: List[List[Any]]
    receipt_data: List[List[Any]]
    payment_pivot_data: List[List[Any]]
    pending_payment_data: List[List[Any]]
    fund_distribution_data: List[Dict[str, Any]]

@router.post("/api/capital-flow", response_model=CapitalFlowResponse)
async def get_capital_flow_data(request: CapitalFlowRequest):
    """
    获取资金流动分析数据
    
    Args:
        request: 包含开始日期和结束日期的请求对象
        
    Returns:
        CapitalFlowResponse: 包含所有资金流动相关数据的响应对象
    """
    try:
        # 这里应该根据日期范围查询数据库
        # 目前返回JSON格式的数据结构
        
        return {
            "fund_status_data": [
                ["项目", "上期", "可用存款", "应付票据", "应付保理", "短期借款", "净存量"],
                ["上期", "¥5,200,000", "¥3,800,000", "¥800,000", "¥400,000", "¥200,000", "¥2,400,000"],
                ["当前", "¥5,800,000", "¥4,200,000", "¥900,000", "¥500,000", "¥300,000", "¥2,500,000"],
                ["变化", "+¥600,000", "+¥400,000", "+¥100,000", "+¥100,000", "+¥100,000", "+¥100,000"]
            ],
            
            "receipt_data": [
                ["收款类型", "总收款"],
                ["货币收款", "¥3,200,000"],
                ["抵房收款", "¥800,000"],
                ["票据收款", "¥600,000"],
                ["代付收款", "¥400,000"],
                ["其他收款", "¥300,000"],
                ["合计", "¥5,300,000"]
            ],
            
            "payment_pivot_data": [
                ["付款类型", "分包", "劳务", "材料", "合计"],
                ["总付款", "¥2,800,000", "¥1,200,000", "¥1,500,000", "¥5,500,000"],
                ["现金", "¥800,000", "¥400,000", "¥300,000", "¥1,500,000"],
                ["保理", "¥1,200,000", "¥500,000", "¥600,000", "¥2,300,000"],
                ["代付付款", "¥600,000", "¥200,000", "¥400,000", "¥1,200,000"],
                ["其他", "¥200,000", "¥100,000", "¥200,000", "¥500,000"]
            ],
            
            "pending_payment_data": [
                [
                    "单据编号",
                    "供应商名称", 
                    "付款类型",
                    "申请金额",
                    "审批状态",
                    "申请日期",
                    "预计付款日期",
                    "项目名称"
                ],
                [
                    "PD20240515001",
                    "北京建筑公司",
                    "分包付款",
                    1200000,
                    "审批中",
                    "2024-05-15",
                    "2024-05-20",
                    "智慧城市项目"
                ],
                [
                    "PD20240514002",
                    "上海钢材公司",
                    "材料付款",
                    800000,
                    "已审批",
                    "2024-05-14",
                    "2024-05-18",
                    "办公楼建设"
                ],
                [
                    "PD20240513003",
                    "广州设备厂",
                    "设备付款",
                    600000,
                    "审批中",
                    "2024-05-13",
                    "2024-05-17",
                    "基础设施项目"
                ],
                [
                    "PD20240512004",
                    "深圳劳务公司",
                    "劳务付款",
                    400000,
                    "已审批",
                    "2024-05-12",
                    "2024-05-16",
                    "智慧城市项目"
                ],
                [
                    "PD20240511005",
                    "成都运输公司",
                    "运输费用",
                    200000,
                    "待审批",
                    "2024-05-11",
                    "2024-05-15",
                    "办公楼建设"
                ],
                [
                    "PD20240510006",
                    "杭州咨询公司",
                    "咨询费用",
                    150000,
                    "审批中",
                    "2024-05-10",
                    "2024-05-14",
                    "基础设施项目"
                ],
                [
                    "PD20240509007",
                    "武汉监理公司",
                    "监理费用",
                    300000,
                    "已审批",
                    "2024-05-09",
                    "2024-05-13",
                    "智慧城市项目"
                ],
                [
                    "PD20240508008",
                    "天津装修公司",
                    "装修费用",
                    500000,
                    "审批中",
                    "2024-05-08",
                    "2024-05-12",
                    "办公楼建设"
                ]
            ],
            
            "fund_distribution_data": [
                {"value": 2800000, "name": "分包付款"},
                {"value": 1200000, "name": "劳务付款"},
                {"value": 1500000, "name": "材料付款"},
                {"value": 800000, "name": "设备付款"},
                {"value": 500000, "name": "其他付款"}
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取资金流动数据失败: {str(e)}")
