<template>
  <div class="salary-tax-view">
    <!-- Page Header -->
     <!--
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <span class="title-icon">💼</span>
          薪酬个税管理系统
        </h1>
        <p class="page-subtitle">高效管理薪酬数据，精准计算个人所得税</p>
      </div>
      <div class="header-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>
    </div>
    -->

    <!-- Data fetch controls -->
    <div class="data-fetch-controls">
      <div class="fetch-controls-group">
        <select v-model="selectedYear" class="year-select">
          <option value="">请选择年份</option>
          <option value="2024">2024年</option>
          <option value="2025">2025年</option>
          <option value="2026">2026年</option>
          <option value="2027">2027年</option>
        </select>
        <button @click="fetchAllData" class="fetch-button" :disabled="!selectedYear || isLoading">
          <span class="button-icon">📥</span>
          <span>拉取数据</span>
        </button>
        <button @click="saveAllData" class="save-button" :disabled="isLoading">
          <span class="button-icon">💾</span>
          <span>保存数据</span>
        </button>
        <span style="margin-left: 16px; color: #888; font-size: 13px;">保存时会自动计算薪酬总表的社保回摊</span>
      </div>
    </div>

    <!-- First row: Action buttons -->
    <div class="action-buttons">
      <button @click="matchIdAndProject" class="action-button match">
        <span class="button-icon">🔗</span>
        <span>匹配身份证号及项目</span>
      </button>
      <button @click="calculateTax" class="action-button tax">
        <span class="button-icon">🧮</span>
        <span>计算个税</span>
      </button>
      <button @click="generateDeclaration" class="action-button declaration">
        <span class="button-icon">📄</span>
        <span>生成申报表</span>
      </button>
      <button @click="generateDivision" class="action-button declaration">
        <span class="button-icon">📑</span>
        <span>生成劳务派遣分割表</span>
      </button>
      <button @click="pushDeclaration" class="action-button push">
        <span class="button-icon">🚀</span>
        <span>构建财务一体化计提发放模板(薪酬发放表)</span>
      </button>
      <button @click="buildFinanceTemplate" class="action-button template">
        <span class="button-icon">🏗️</span>
        <span>构建财务一体化计提发放模板(社保公积金表)</span>
      </button>
      <button @click="exportAllTablesToExcel" class="action-button export-all">
        <span class="button-icon">💾</span>
        <span>导出所有表格</span>
      </button>
      <button @click="undoLastAction" class="action-button undo" :disabled="undoStack.length === 0">
        <span class="button-icon">↩️</span>
        <span>撤销操作</span>
      </button>
    </div>

    <!-- Second row: Tab navigation -->
    <div class="tabs-container">
      <div class="tabs">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab', { active: activeTabIndex === index }]"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </div>
      </div>
    </div>
    
    <!-- Loading overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p class="loading-text">处理中...</p>
      </div>
    </div>

    <!-- VTable container -->
    <div class="vtable-container" :class="{ 'loading': isLoading }">
      <!-- 使用 v-show 替代 v-if，保持所有组件实例存在 -->
      <VTableComponent
        v-show="activeTabIndex === 0"
        ref="taxDeclarationTable"
        :key="'tab-0'"
        :data="taxDeclarationData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 0)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 1"
        ref="taxCalculationTable"
        :key="'tab-1'"
        :data="taxCalculationData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 1)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 2"
        ref="subsidyPackageTable"
        :key="'tab-2'"
        :data="subsidyPackageData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 2)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 3"
        ref="bonusTable"
        :key="'tab-3'"
        :data="bonusData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 3)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 4"
        ref="outsourcingSalaryTable"
        :key="'tab-4'"
        :data="outsourcingSalaryData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 4)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 5"
        ref="socialSecurityAndHousingFundTable"
        :key="'tab-5'"
        :data="socialSecurityAndHousingFundData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 5)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 6"
        ref="socialSecurityAndHousingFundAdjustmentTable"
        :key="'tab-6'"
        :data="socialSecurityAndHousingFundAdjustmentData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 6)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 7"
        ref="specialDeductionTable"
        :key="'tab-7'"
        :data="specialDeductionData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 7)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 8"
        ref="remoteTaxTable"
        :key="'tab-8'"
        :data="remoteTaxData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 8)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 9"
        ref="idCardTable"
        :key="'tab-9'"
        :data="idCardData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 9)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
      <VTableComponent
        v-show="activeTabIndex === 10"
        ref="projectMappingTable"
        :key="'tab-10'"
        :data="projectMappingData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :auto-width="true"
        @data-change="(newData) => handleDataChange(newData, 10)"
        @cell-edit="handleCellEdit"
        @row-delete="handleRowDelete"
        @row-add="handleRowAdd"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import VTableComponent from '@/components/VTableComponent.vue';
import * as ExcelJS from 'exceljs';
import { exportMultiSheetExcel } from '@/scripts/excelUtils.js';
import { createTaxCalculator } from '@/scripts/taxCalculator.js';
import taxCalculatorTs from '../scripts/taxCalculator.ts';

// Tab definitions
const tabs = [
  { name: '个税申报表', key: 'taxDeclaration' },
  { name: '算税底稿', key: 'taxCalculation' },
  { name: '包干费', key: 'subsidyPackage' },
  { name: '一次性年终奖', key: 'bonus' },
  { name: '外包薪酬表', key: 'outsourcingSalary' },
  { name: '社保公积金实际缴纳表', key: 'socialSecurityAndHousingFund' },
  { name: '薪酬总表(无一次性年终)', key: 'socialSecurityAndHousingFundAdjustment' },
  { name: '累计专项附加扣除表', key: 'specialDeduction' },
  { name: '异地纳税', key: 'remoteTax' },
  { name: '身份证号表', key: 'idCard' },
  { name: '项目匹配表', key: 'projectMapping' },
];

// State variables
const activeTabIndex = ref(0);
const isLoading = ref(false);
const selectedYear = ref('');
// Table configuration
const tableWidth = ref(1600);
const tableHeight = ref(500);

// 创建税务计算器实例
const taxCalculator = createTaxCalculator();

// 撤销功能相关状态
const undoStack = ref([]);
const maxUndoSteps = 10;

// 表格组件引用
const taxDeclarationTable = ref(null);
const taxCalculationTable = ref(null);
const subsidyPackageTable = ref(null);
const bonusTable = ref(null);
const outsourcingSalaryTable = ref(null);
const socialSecurityAndHousingFundTable = ref(null);
const socialSecurityAndHousingFundAdjustmentTable = ref(null);
const specialDeductionTable = ref(null);
const remoteTaxTable = ref(null);
const idCardTable = ref(null);
const projectMappingTable = ref(null);

// 数据缓存机制 - 用于保存每个表格的实时修改状态
const dataCache = ref({
  0: null, // taxDeclarationData
  1: null, // taxCalculationData
  2: null, // subsidyPackageData
  3: null, // bonusData
  4: null, // outsourcingSalaryData
  5: null, // socialSecurityAndHousingFundData
  6: null, // socialSecurityAndHousingFundAdjustmentData
  7: null, // specialDeductionData
  8: null, // remoteTaxData
  9: null, // idCardData
  10: null // projectMappingData
});

// Table data for salary tax management
// (1) 个税申报表
const taxDeclarationData = ref([
  [
    '工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
    '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
    '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
    '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
    '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
    '准予扣除的捐赠额', '减免税额', '备注'
  ],
  [
    'E001', '张三', '身份证', '110101199001011234', 15000, 0,
    800, 240, 80, 960, 1000, 0, 1000, 0, 2000, 0, 0, 0, 0, 0, 0, 0, 0, ''
  ],
  [
    'E002', '李四', '身份证', '110101199102022345', 12000, 0,
    750, 200, 60, 900, 1000, 400, 0, 1500, 2000, 0, 0, 0, 0, 0, 0, 0, 0, ''
  ],
  [
    'E003', '王五', '身份证', '110101199203033456', 18000, 0,
    900, 300, 90, 1080, 2000, 0, 1000, 0, 2000, 1000, 0, 0, 0, 0, 0, 0, 0, ''
  ]
]);

// (2) 算税底稿
const taxCalculationData = ref([
  [
    '月份', '算税地区','社保缴纳单位','公积金缴纳单位', '身份证号', '姓名', '成本所属项目','财务标准项目','财务标准单位', '薪酬类别',
    '本期收入', '基本养老保险费', '住房公积金', '基本医疗保险费',
    '失业保险费', '企业(职业)年金', '其它扣款', '调整收入', '调整扣除',
    '调整累计个税', '累计社保', '累计专项附加', '累计法定扣除',
    '累计调整扣除', '累计收入', '累计扣除', '累计应扣税款',
    '累计上次税款', '本次税款', '本次达到税率', '一次性年终奖校验','备注'
    
  ],
  [
    '2025-01', '北京市', '华南', '武汉1', '110101199001011234', '张三', '研发项目A','财务一体化项目1', '财务一体化单位1', '工资',
    15000, 800, 960, 240, 80, 0, 0, 0, 0, 0, 2080, 4000, 5000, 0,
    15000, 11080, 595, 0, 595, '10%', '否','备注1'
  ],
  [
    '2025-01', '北京市', '华南', '武汉1', '110101199102022345', '李四', '市场项目B','财务一体化项目2', '财务一体化单位2', '工资',
    12000, 750, 900, 200, 60, 0, 0, 0, 0, 0, 1910, 4900, 5000, 0,
    12000, 11810, 19, 0, 19, '3%', '否','备注2'
  ]
]);

// (3) 累计专项附加扣除表
const specialDeductionData = ref([
  [
    '工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止',
    '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息',
    '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'
  ],
  [
    'E001', '张三', '身份证', '110101199001011234', '2025-01-01', '2025-01-31',
    15000, 1000, 0, 1000, 0, 2000, 0, 0
  ],
  [
    'E002', '李四', '身份证', '110101199102022345', '2025-01-01', '2025-01-31',
    12000, 1000, 400, 0, 1500, 2000, 0, 0
  ],
  [
    'E003', '王五', '身份证', '110101199203033456', '2025-01-01', '2025-01-31',
    18000, 2000, 0, 1000, 0, 2000, 1000, 0
  ]
]);

// (4) 异地纳税
const remoteTaxData = ref([
  [
    '预留', '身份证号', '姓名', '一月', '二月', '三月', '四月', '五月', '六月',
    '七月', '八月', '九月', '十月', '十一月', '十二月'
  ],
  [
    '', '110101199001011234', '张三', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
  ],
  [
    '', '110101199102022345', '李四', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
  ],
  [
    '', '110101199203033456', '王五', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
  ]
]);

// (5) 身份证号表
const idCardData = ref([
  ['姓名', '身份证号','银行卡号','开户行名称','开户行编码','备注'],
  ['张三', '110101199001011234','6217000010000000000','中国银行','9999999999999999999','备注1'],
  ['李四', '110101199102022345','6217000020000000000','中国银行','9999999999999999999','备注2'],
  ['王五', '110101199203033456','6217000030000000000','中国银行','9999999999999999999','备注3'],
  ['赵六', '110101199304044567','6217000040000000000','中国银行','9999999999999999999','备注4']
]);

// (6) 项目匹配表
const projectMappingData = ref([
  ['人资项目名称', '标准项目名称', '标准项目编码','财务标准单位','财务标准单位编码'],
  ['人资项目1', '财务一体化项目1', '12211235664','财务一体化单位1','财务一体化单位编码1'],

]);

// (7) 包干费
const subsidyPackageData = ref([
  ['月份', '身份证号', '姓名', '成本所属项目', '财务标准项目', '工作餐补贴','交通补贴','通讯补贴','住房补贴','取证补贴','其他补贴','合计补贴','备注'],
  ['2025-01', '110101199001011234', '张三', '研发项目A', '财务一体化项目1', 15000, 800, 960, 240, 80,80,80,"备注1" ],
  ['2025-01', '110101199102022345', '李四', '市场项目B', '财务一体化项目2', 15000, 800, 960, 240, 80,80,80,"备注1" ]
 ]);

// (8) 一次性年终奖
const bonusData = ref([
  ['缴纳月份', '算税地区',  '身份证号', '姓名', '成本所属项目', '财务标准项目', '薪酬类别', '原奖金金额','报税收入','税额','税率','备注'],
  ['2025-01', '北京市', '110101199001011234', '张三', '研发项目A', '财务一体化项目1', '商务兑现', 15000, 15000, 595, '10%','备注1'],
  ['2025-01', '北京市', '110101199102022345', '李四', '市场项目B', '财务一体化项目2', '年终兑现', 12000, 12000, 19, '3%','备注2']
]);

// (9) 外包薪酬表
const outsourcingSalaryData = ref([
  ['月份','外包公司','姓名','身份证号','人资项目','财务标准项目','总金额','进项税','入账成本','薪酬类别','备注'],
  ['2025-01','外包公司1','张三','110101199001011234','人资项目1','财务标准项目1',15000,595,15000,'工资','备注1'],
  ['2025-01','外包公司2','李四','110101199102022345','人资项目2','财务标准项目2',12000,19,12000,'工资','备注2']
]);

// (10) 社保公积金实际缴纳表
const socialSecurityAndHousingFundData = ref([
  ['月份','代缴单位','个人公积金','个人养老','个人医疗','个人失业','个人年金','补充医疗','单位公积金','单位养老','单位医疗','单位失业','工伤金额','单位生育','企业年金','合计金额','备注'],
  ['2025-01','代缴单位1',15000,15000,15000,15000,15000,15000,15000,15000,15000,15000,15000,15000,15000,15000,'备注1'],
  ['2025-01','代缴单位2',12000,12000,12000,12000,12000,12000,12000,12000,12000,12000,12000,12000,12000,12000,'备注2']
]);

// (11) 社保公积金调整差额表
const socialSecurityAndHousingFundAdjustmentData = ref( [[
    '月份','社保缴纳单位','公积金缴纳单位', '身份证号', '姓名', '成本所属项目','财务标准项目','财务标准单位', '薪酬类别',
    '本期收入', '个人养老回摊', '个人公积金回摊', '个人医疗回摊',
    '个人失业回摊', '个人年金回摊', '其它扣款', '包干费补贴', '本次个税',
    '单位养老分摊', '单位公积金分摊', '单位医疗分摊', '单位失业分摊',
    '单位工伤分摊', '单位生育分摊', '补充医疗分摊', '其他分摊',
    '总成本','备注'
    
  ],
  [
    '2025-01', '华南', '武汉1', '110101199001011234', '张三', '研发项目A','财务一体化项目1', '财务一体化单位1', '工资',
    15000, 800, 960, 240,
     80, 0, 0, 0, 0,
      0, 2080, 4000, 5000, 0,
    15000, 11080,1,1,'备注1'
  ],
  [
    '2025-01', '华南', '武汉1', '110101199102022345', '李四', '市场项目B','财务一体化项目2', '财务一体化单位2', '工资',
    12000, 750, 900, 200, 60, 0, 0, 0, 0, 0, 1910, 4900, 5000, 0,
    12000, 11810,1,1,'备注2'
  ]]);

// (11) 累计专项附加扣除表

// Methods for salary tax management

// UI Methods
const switchTab = (index) => {
  activeTabIndex.value = index;

  // 如果有缓存数据，恢复它
  if (dataCache.value[index]) {
    const activeData = getActiveTableData(index);
    if (activeData) {
      activeData.value = JSON.parse(JSON.stringify(dataCache.value[index]));
      console.log(`Restored cached data for tab ${index}`);
    }
  }

  // 切换 tab 后，需要刷新当前显示的表格尺寸
  nextTick(() => {
    const currentTableRef = getCurrentTableRef();
    if (currentTableRef && currentTableRef.getTableInstance) {
      const tableInstance = currentTableRef.getTableInstance();
      if (tableInstance) {
        tableInstance.resize();
      }
    }
  });
};


// 运算函数 (Business Logic Functions)

// (3) 匹配身份证号及项目
const matchIdAndProject = () => {
  const idMap = new Map(idCardData.value.slice(1).map(row => [row[0], row[1]])); // 姓名 -> 身份证号
  const projectMap = new Map(projectMappingData.value.slice(1).map(row => [row[0], row[1]])); // 人资项目名称 -> 标准项目名称
  const standardUnitMap = new Map(projectMappingData.value.slice(1).map(row => [row[1], row[3]])); // 标准项目名称 -> 财务标准单位
  console.log(projectMap);
 

  const oldData = JSON.parse(JSON.stringify(taxCalculationData.value));
  const header = taxCalculationData.value[0];
  const nameIndex = header.indexOf('姓名');
  const idIndex = header.indexOf('身份证号');
  const projectIndex = header.indexOf('成本所属项目');
  const standardProjectIndex = header.indexOf('财务标准项目');
  const standardUnitIndex = header.indexOf('财务标准单位');


  if (nameIndex === -1 || idIndex === -1) {
      alert('算税底稿表头不正确，无法匹配身份证号！');
      return;
  }

  let matchCount = 0;
  taxCalculationData.value.slice(1).forEach(row => {
    const name = row[nameIndex];
    if (!row[idIndex] && idMap.has(name)) {
      row[idIndex] = idMap.get(name);
      matchCount++;
    }
    const project = row[projectIndex];
    if (projectIndex !== -1 && standardProjectIndex !== -1 && standardUnitIndex !== -1 && projectMap.has(project)) {
      let projectStd = projectMap.get(project);
      row[standardProjectIndex] = projectStd;
      row[standardUnitIndex] = standardUnitMap.get(projectStd);
      matchCount++;
    }
  });

  // 刷新表格数据显示
  refreshTableData(1);

  pushToUndoStack({ type: 'bulk', tabIndex: 1, oldData });
  alert(`匹配完成！共为 ${matchCount} 条记录自动填充了身份证号及项目。` );
  switchTab(1);
};

// (4) 计算个税
const calculateTax = async () => {
  try {
    isLoading.value = true;
    const oldData = JSON.parse(JSON.stringify(taxCalculationData.value));
    const specialDeductionDataValue=JSON.parse(JSON.stringify(specialDeductionData.value));
    const result = await taxCalculatorTs(oldData,specialDeductionDataValue);
    taxCalculationData.value=result;

    // 刷新表格数据显示
    refreshTableData(1);

    pushToUndoStack({ type: 'bulk', tabIndex: 1, oldData });
    alert('个税计算完成！');
    switchTab(1);
  } catch (error) {
    console.error('计算个税失败:', error);
    alert('计算个税失败: ' + error.message);
  } finally {
    isLoading.value = false;
  }
};

// (5) 生成申报表
const generateDeclaration = async () => {
  try {
    // 1. 获取所有月份
    const header = taxCalculationData.value[0];
    const monthIdx = header.indexOf('月份');
    if (monthIdx === -1) {
      alert('算税底稿缺少"月份"字段！');
      return;
    }
    const months = Array.from(new Set(taxCalculationData.value.slice(1).map(row => row[monthIdx])));

    // 2. 弹窗选择月份
    const selectedMonth = await new Promise((resolve) => {
      const monthOptions = months.map(m => `<option value="${m}">${m}</option>`).join('');
      const dialog = document.createElement('div');
      dialog.style.cssText = `
        position: fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;
        background:rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;`;
      dialog.innerHTML = `
        <div style="background:white;padding:24px 32px;border-radius:8px;min-width:240px;">
          <h3>请选择要生成申报表的月份</h3>
          <select id="monthSelect" style="width:100%;margin:16px 0;">${monthOptions}</select>
          <div style="text-align:right;">
            <button id="cancelBtn" style="margin-right:10px;">取消</button>
            <button id="okBtn" style="background:#3b82f6;color:white;padding:4px 16px;border:none;border-radius:4px;">确定</button>
          </div>
        </div>`;
      document.body.appendChild(dialog);
      dialog.querySelector('#cancelBtn').onclick = () => { document.body.removeChild(dialog); resolve(null); };
      dialog.querySelector('#okBtn').onclick = () => {
        const val = dialog.querySelector('#monthSelect').value;
        document.body.removeChild(dialog);
        resolve(val);
      };
    });
    if (!selectedMonth) return;

    // 3. 筛选出该月份的数据
    const filteredRows = taxCalculationData.value.slice(1).filter(row => row[monthIdx] === selectedMonth);

    // 4. 按"身份证号+算税地区"分组汇总
    const idIdx = header.indexOf('身份证号');
    const regionIdx = header.indexOf('算税地区');
    const nameIdx = header.indexOf('姓名');
    const empIdIdx = header.indexOf('工号');
    const remarkIdx = header.indexOf('备注');
    if (idIdx === -1 || regionIdx === -1) {
      alert('算税底稿缺少"身份证号"或"算税地区"字段！');
      return;
    }
    // 申报表字段顺序和映射（如需调整请根据实际表头）
    const declarationHeader = [
      '工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
      '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
      '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
      '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
      '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
      '准予扣除的捐赠额', '减免税额', '备注'
    ];
    // 建立算税底稿字段到申报表字段的映射（如需调整请根据实际表头）
    const fieldMap = {
      '工号': '工号',
      '姓名': '姓名',
      '证件类型': '证件类型', // 默认身份证
      '证件号码': '身份证号',
      '本期收入': '本期收入',
      '本期免税收入': '本期免税收入',
      '基本养老保险费': '基本养老保险费',
      '基本医疗保险费': '基本医疗保险费',
      '失业保险费': '失业保险费',
      '住房公积金': '住房公积金',
      '累计子女教育': '累计子女教育',
      '累计继续教育': '累计继续教育',
      '累计住房贷款利息': '累计住房贷款利息',
      '累计住房租金': '累计住房租金',
      '累计赡养老人': '累计赡养老人',
      '累计3岁以下婴幼儿照护': '累计3岁以下婴幼儿照护',
      '累计个人养老金': '累计个人养老金',
      '企业(职业)年金': '企业(职业)年金',
      '商业健康保险': '商业健康保险',
      '税延养老保险': '税延养老保险',
      '其他': '其他',
      '准予扣除的捐赠额': '准予扣除的捐赠额',
      '减免税额': '减免税额',
      '备注': '备注'
    };
    // 需要合计的字段
    const sumFields = [
      '本期收入', '本期免税收入', '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
      '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金', '累计赡养老人',
      '累计3岁以下婴幼儿照护', '累计个人养老金', '企业(职业)年金', '商业健康保险', '税延养老保险',
      '其他', '准予扣除的捐赠额', '减免税额'
    ];
    // 5. 分组并合计
    const groupMap = new Map();
    for (const row of filteredRows) {
      const id = row[idIdx];
      const region = row[regionIdx];
      const key = `${id}_${region}`;
      if (!groupMap.has(key)) {
        groupMap.set(key, []);
      }
      groupMap.get(key).push(row);
    }
    // 6. 生成申报表数据
    const newDeclarationData = [declarationHeader];
    for (const [key, rows] of groupMap.entries()) {
      const first = rows[0];
      const rowData = declarationHeader.map(col => {
        if (sumFields.includes(col)) {
          // 找到映射到算税底稿的字段名
          const srcField = Object.keys(fieldMap).find(k => fieldMap[k] === col);
          const idx = header.indexOf(srcField);
          if (idx === -1) return 0;
          return rows.reduce((sum, r) => sum + (parseFloat(r[idx]) || 0), 0);
        }
        if (col === '工号') return empIdIdx !== -1 ? first[empIdIdx] : '';
        if (col === '姓名') return nameIdx !== -1 ? first[nameIdx] : '';
        if (col === '证件类型') return '身份证';
        if (col === '证件号码') return first[idIdx] || '';
        if (col === '备注') return remarkIdx !== -1 ? first[remarkIdx] : '';
        return '';
      });
      newDeclarationData.push(rowData);
    }
    // 7. 保存撤销、刷新、切tab
    const oldData = JSON.parse(JSON.stringify(taxDeclarationData.value));
    taxDeclarationData.value = newDeclarationData;
    refreshTableData(0);
    pushToUndoStack({ type: 'bulk', tabIndex: 0, oldData });
    alert('个税申报表生成成功！');
    switchTab(0);
  } catch (error) {
    console.error('生成申报表失败:', error);
    alert('生成申报表失败: ' + error.message);
  }
};

// (6) 生成一体化发放单
const pushDeclaration = async () => {
  const Data = taxCalculationData.value;
  const Data1 = idCardData.value;
  const Data2= projectMappingData.value;
  const header = Data[0];
  const salaryCategoryIndex = header.indexOf('薪酬类别');
  const monthIndex = header.indexOf('月份');

  if (salaryCategoryIndex === -1 || monthIndex === -1) {
    alert('算税底稿表头不正确，无法生成一体化发放单！');
    return;
  }
  
  // 提取薪酬类别和月份
  const salaryCategories = new Set();
  const months = new Set();
  
  // 从算税底稿数据中提取信息（假设第一行是表头）
  if (Data.length > 1) {
    Data.slice(1).forEach(row => {
      // 假设薪酬类别在第3列，月份在第2列（根据实际数据结构调整）
      if (row[salaryCategoryIndex]) salaryCategories.add(row[salaryCategoryIndex]);
      if (row[monthIndex]) months.add(row[monthIndex]);
    });
  }

  // 创建选择弹窗
  const selectedCategory = await new Promise((resolve) => {
    const categoryOptions = Array.from(salaryCategories).map(cat => 
      `<option value="${cat}">${cat}</option>`
    ).join('');
    
    const monthOptions = Array.from(months).map(month => 
      `<option value="${month}">${month}</option>`
    ).join('');

    const dialog = document.createElement('div');
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    `;
    
    dialog.innerHTML = `
      <div style="background: white; padding: 20px; border-radius: 8px; min-width: 300px;">
        <h3>选择薪酬类别和月份</h3>
        <div style="margin: 10px 0;">
          <label>薪酬类别：</label>
          <select id="categorySelect" style="width: 100%; padding: 5px; margin-top: 5px;">
            ${categoryOptions}
          </select>
        </div>
        <div style="margin: 10px 0;">
          <label>月份：</label>
          <select id="monthSelect" style="width: 100%; padding: 5px; margin-top: 5px;">
            ${monthOptions}
          </select>
        </div>
        <div style="text-align: right; margin-top: 20px;">
          <button id="cancelButton" style="margin-right: 10px; padding: 5px 15px;">取消</button>
          <button id="confirmButton" style="padding: 5px 15px; background: #007bff; color: white; border: none; border-radius: 3px;">
            确定
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(dialog);

    const cancelButton = dialog.querySelector('#cancelButton');
    const confirmButton = dialog.querySelector('#confirmButton');

    const checkSelection = setInterval(() => {
      if (window.selectedValues || window.dialogCancelled) {
        clearInterval(checkSelection);
        document.body.removeChild(dialog);
        if (window.dialogCancelled) {
          resolve(null); // Resolve with null if cancelled
          delete window.dialogCancelled;
        }
        if (window.selectedValues) {
          resolve(window.selectedValues);
          delete window.selectedValues;
        }
      }
    }, 100);

    cancelButton.onclick = () => {
      window.dialogCancelled = true;
    };

    confirmButton.onclick = () => {
      const category = document.getElementById('categorySelect').value;
      const month = document.getElementById('monthSelect').value;
      window.selectedValues = {category, month};
    };
  });

  // If user cancels, selectedCategory will be null
  if (!selectedCategory) {
    alert('操作已取消。');
    return;
  }

  // 发送POST请求
  
  try {
    const response = await fetch('http://localhost:8000/api/push-declaration', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        year: selectedYear.value,
        category: selectedCategory.category,
        month: selectedCategory.month,
        salayData: Data,
        idData:Data1,
        projectData:Data2

      })
    });

    if (!response.ok) {
      throw new Error(`推送失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    alert('一体化发放单推送成功！');
    console.log('推送结果:', result);
  } catch (error) {
    console.error('推送失败:', error);
    alert('推送失败: ' + error.message);
  }
};


// (7) 构建财务一体化计提发放模板
const buildFinanceTemplate = () => {
  alert('此功能尚未实现。');
};



// (10) 导出所有表格
const exportAllTablesToExcel = async () => {
  try {
    isLoading.value = true;
    const sheets = tabs.map((tab, index) => {
      const dataRef = getActiveTableData(index);
      return {
        name: tab.name,
        data: dataRef ? dataRef.value : [[]]
      };
    }).filter(sheet => sheet.data && sheet.data.length > 1);

    if (sheets.length === 0) {
      alert('没有数据可以导出。');
      return;
    }

    await exportMultiSheetExcel(sheets, '薪酬个税全量数据');
    alert('导出成功！');
  } catch (error) {
    console.error('导出所有表格失败:', error);
    alert('导出所有表格失败: ' + error.message);
  } finally {
    isLoading.value = false;
  }
};

// (11) 拉取所有数据
const fetchAllData = async () => {
  if (!selectedYear.value) {
    alert('请先选择年份！');
    return;
  }

  try {
    isLoading.value = true;

    // 构建请求参数
    const requestData = {
      filters: {
        fiscalYear: selectedYear.value
      },
      timestamp: new Date().toISOString()
    };

    // 发送请求到后端获取薪酬个税数据
    const response = await fetch('http://localhost:8000/api/salary-tax/fetch-all', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`拉取数据失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    // 更新各个表格的数据
    if (result.data) {
      // 保存当前状态到撤销栈
      const oldStates = {
        taxDeclaration: JSON.parse(JSON.stringify(taxDeclarationData.value)),
        taxCalculation: JSON.parse(JSON.stringify(taxCalculationData.value)),
        specialDeduction: JSON.parse(JSON.stringify(specialDeductionData.value)),
        remoteTax: JSON.parse(JSON.stringify(remoteTaxData.value)),
        idCard: JSON.parse(JSON.stringify(idCardData.value)),
        projectMapping: JSON.parse(JSON.stringify(projectMappingData.value)),
        subsidyPackage: JSON.parse(JSON.stringify(subsidyPackageData.value)),
        bonus: JSON.parse(JSON.stringify(bonusData.value)),
        outsourcingSalary: JSON.parse(JSON.stringify(outsourcingSalaryData.value)),
        socialSecurityAndHousingFund: JSON.parse(JSON.stringify(socialSecurityAndHousingFundData.value)),
        socialSecurityAndHousingFundAdjustment: JSON.parse(JSON.stringify(socialSecurityAndHousingFundAdjustmentData.value))
      };

      // 更新数据
      if (result.data.taxDeclaration) {
        taxDeclarationData.value = result.data.taxDeclaration;
        refreshTableData(0);
      }
      if (result.data.taxCalculation) {
        taxCalculationData.value = result.data.taxCalculation;
        refreshTableData(1);
      }
      if (result.data.specialDeduction) {
        specialDeductionData.value = result.data.specialDeduction;
        refreshTableData(7);
      }
      if (result.data.remoteTax) {
        remoteTaxData.value = result.data.remoteTax;
        refreshTableData(8);
      }
      if (result.data.idCard) {
        idCardData.value = result.data.idCard;
        refreshTableData(9);
      }
      if (result.data.projectMapping) {
        projectMappingData.value = result.data.projectMapping;
        refreshTableData(10);
      }
      if (result.data.subsidyPackage) {
        subsidyPackageData.value = result.data.subsidyPackage;
        refreshTableData(2);
      }
      if (result.data.bonus) {
        bonusData.value = result.data.bonus;
        refreshTableData(3);
      }
      if (result.data.outsourcingSalary) {
        outsourcingSalaryData.value = result.data.outsourcingSalary;
        refreshTableData(4);
      }
      if (result.data.socialSecurityAndHousingFund) {
        socialSecurityAndHousingFundData.value = result.data.socialSecurityAndHousingFund;
        refreshTableData(5);
      }
      if (result.data.socialSecurityAndHousingFundAdjustment) {
        socialSecurityAndHousingFundAdjustmentData.value = result.data.socialSecurityAndHousingFundAdjustment;
        refreshTableData(6);
      }

      // 保存到撤销栈
      pushToUndoStack({ type: 'bulk_fetch', oldStates });

      alert(`成功拉取 ${selectedYear.value} 年度薪酬个税数据！`);
    } else {
      alert('未获取到有效数据');
    }
  } catch (error) {
    console.error('拉取数据失败:', error);
    alert('拉取数据失败: ' + error.message);
  } finally {
    isLoading.value = false;
  }
};

// (12) 保存所有数据
const saveAllData = async () => {
  try {
    isLoading.value = true;

    // 收集所有表格数据
    const allData = {
      year: selectedYear.value || new Date().getFullYear().toString(),
      taxDeclaration: taxDeclarationData.value,
      taxCalculation: taxCalculationData.value,
      specialDeduction: specialDeductionData.value,
      remoteTax: remoteTaxData.value,
      idCard: idCardData.value,
      projectMapping: projectMappingData.value,
      subsidyPackage: subsidyPackageData.value,
      bonus: bonusData.value,
      outsourcingSalary: outsourcingSalaryData.value,
      socialSecurityAndHousingFund: socialSecurityAndHousingFundData.value,
      socialSecurityAndHousingFundAdjustment: socialSecurityAndHousingFundAdjustmentData.value,
      timestamp: new Date().toISOString()
    };

    // 发送保存请求到后端
    const response = await fetch('http://localhost:8000/api/salary-tax/save-all', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(allData)
    });

    if (!response.ok) {
      throw new Error(`保存数据失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    // === 新增：自动更新薪酬总表（社保回摊） ===
    if (result.data && result.data.socialSecurityAndHousingFundAdjustment) {
      socialSecurityAndHousingFundAdjustmentData.value = result.data.socialSecurityAndHousingFundAdjustment;
      refreshTableData(6); // 刷新第6个tab（薪酬总表）
    }
    // === 新增 END ===
    alert('所有数据保存成功！');
    console.log('保存结果:', result);
  } catch (error) {
    console.error('保存数据失败:', error);
    alert('保存数据失败: ' + error.message);
  } finally {
    isLoading.value = false;
  }
};

// (13) 下载导入模板
const createSpecialDeductionTemplate = async () => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('专项附加扣除模板');

    // 获取表头
    const headers = specialDeductionData.value[0];

    // 设置表头
    const headerRow = worksheet.addRow(headers);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, color: { argb: 'FFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // 添加示例数据行
    const exampleRow = [
      'E999', '示例员工', '身份证', '110101199001011234', '2025-01-01', '2025-01-31',
      15000, 1000, 0, 1000, 0, 2000, 0, 0
    ];
    const dataRow = worksheet.addRow(exampleRow);
    dataRow.eachCell((cell) => {
      cell.alignment = { horizontal: 'left', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // 自动调整列宽
    worksheet.columns.forEach((column, index) => {
      let maxLength = headers[index]?.length || 10;
      maxLength = Math.max(maxLength, String(exampleRow[index] || '').length);
      column.width = Math.min(Math.max(maxLength + 2, 10), 30);
    });

    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `专项附加扣除导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('专项附加扣除模板下载成功');
  } catch (error) {
    console.error('创建模板失败:', error);
    alert('创建模板失败: ' + error.message);
  }
};

// 辅助函数
// 获取指定索引（或当前激活）对应的表格数据
const getActiveTableData = (index = activeTabIndex.value) => {
  switch (index) {
    case 0: return taxDeclarationData;
    case 1: return taxCalculationData;
    case 2: return subsidyPackageData;
    case 3: return bonusData;
    case 4: return outsourcingSalaryData;
    case 5: return socialSecurityAndHousingFundData;
    case 6: return socialSecurityAndHousingFundAdjustmentData;
    case 7: return specialDeductionData;
    case 8: return remoteTaxData;
    case 9: return idCardData;
    case 10: return projectMappingData;
    default: return null;
  }
};

// 获取当前激活的表格组件引用
const getCurrentTableRef = () => {
  switch (activeTabIndex.value) {
    case 0: return taxDeclarationTable.value;
    case 1: return taxCalculationTable.value;
    case 2: return subsidyPackageTable.value;
    case 3: return bonusTable.value;
    case 4: return outsourcingSalaryTable.value;
    case 5: return socialSecurityAndHousingFundTable.value;
    case 6: return socialSecurityAndHousingFundAdjustmentTable.value;
    case 7: return specialDeductionTable.value;
    case 8: return remoteTaxTable.value;
    case 9: return idCardTable.value;
    case 10: return projectMappingTable.value;
    default: return null;
  }
};

// 获取指定索引的表格组件引用
const getTableRefByIndex = (index) => {
  switch (index) {
    case 0: return taxDeclarationTable.value;
    case 1: return taxCalculationTable.value;
    case 2: return subsidyPackageTable.value;
    case 3: return bonusTable.value;
    case 4: return outsourcingSalaryTable.value;
    case 5: return socialSecurityAndHousingFundTable.value;
    case 6: return socialSecurityAndHousingFundAdjustmentTable.value;
    case 7: return specialDeductionTable.value;
    case 8: return remoteTaxTable.value;
    case 9: return idCardTable.value;
    case 10: return projectMappingTable.value;
    default: return null;
  }
};

// 刷新指定表格的数据显示
const refreshTableData = (tabIndex) => {
  const tableRef = getTableRefByIndex(tabIndex);
  const tableData = getActiveTableData(tabIndex);

  if (tableRef && tableData && tableRef.setData) {
    // 使用 VTableComponent 的 setData 方法强制刷新
    tableRef.setData(tableData.value);
    console.log(`Refreshed table data for tab ${tabIndex}`);
  }
};

// 刷新所有表格的数据显示
const refreshAllTables = () => {
  for (let i = 0; i <= 10; i++) {
    refreshTableData(i);
  }
};



// 核心业务逻辑函数
const pushToUndoStack = (action) => {
  undoStack.value.push(action);
  if (undoStack.value.length > maxUndoSteps) {
    undoStack.value.shift();
  }
};

// VTable Event Handlers
const handleDataChange = (newData, tabIndex = null) => {
  // 如果提供了 tabIndex，使用它；否则使用当前激活的 tab
  const targetTabIndex = tabIndex !== null ? tabIndex : activeTabIndex.value;
  const activeData = getActiveTableData(targetTabIndex);
  if (activeData) {
    // 更新主数据
    activeData.value = newData;
    // 同时更新缓存
    dataCache.value[targetTabIndex] = JSON.parse(JSON.stringify(newData));
    console.log(`Tab ${targetTabIndex} data updated and cached.`);
  }
};

const handleCellEdit = (editInfo) => {
  console.log('Cell edited:', editInfo);
  // 可在此处添加撤销堆栈逻辑
};

const handleRowDelete = (deleteInfo) => {
  console.log('Row deleted:', deleteInfo);
  // 可在此处添加撤销堆栈逻辑
};

const handleRowAdd = (addInfo) => {
  console.log('Row added:', addInfo);
  // 可在此处添加撤销堆栈逻辑
};

// 撤销操作
const undoLastAction = () => {
  const lastAction = undoStack.value.pop();
  if (!lastAction) return;

  let dataRef;
  switch (lastAction.tabIndex) {
      case 0: dataRef = taxDeclarationData; break;
      case 1: dataRef = taxCalculationData; break;
      case 2: dataRef = specialDeductionData; break;
      case 3: dataRef = remoteTaxData; break;
      case 4: dataRef = idCardData; break;
      case 5: dataRef = projectMappingData; break;
      default: return;
  }

  switch (lastAction.type) {
    case 'edit':
      dataRef.value[lastAction.rowIndex][lastAction.colIndex] = lastAction.oldValue;
      refreshTableData(lastAction.tabIndex);
      break;
    case 'delete':
      for (const row of lastAction.rows) {
        dataRef.value.splice(row.index, 0, row.data);
      }
      refreshTableData(lastAction.tabIndex);
      break;
    case 'add':
      dataRef.value.splice(lastAction.index, 1);
      refreshTableData(lastAction.tabIndex);
      break;
    case 'bulk':
      dataRef.value = lastAction.oldData;
      refreshTableData(lastAction.tabIndex);
      break;
    case 'bulk_fetch':
      // 恢复所有表格数据
      taxDeclarationData.value = lastAction.oldStates.taxDeclaration;
      taxCalculationData.value = lastAction.oldStates.taxCalculation;
      specialDeductionData.value = lastAction.oldStates.specialDeduction;
      remoteTaxData.value = lastAction.oldStates.remoteTax;
      idCardData.value = lastAction.oldStates.idCard;
      projectMappingData.value = lastAction.oldStates.projectMapping;
      subsidyPackageData.value = lastAction.oldStates.subsidyPackage;
      bonusData.value = lastAction.oldStates.bonus;
      outsourcingSalaryData.value = lastAction.oldStates.outsourcingSalary;
      socialSecurityAndHousingFundData.value = lastAction.oldStates.socialSecurityAndHousingFund;
      socialSecurityAndHousingFundAdjustmentData.value = lastAction.oldStates.socialSecurityAndHousingFundAdjustment;
      // 刷新所有表格
      refreshAllTables();
      break;
  }
};
</script>

<style scoped>
.salary-tax-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 12px;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

/* Page Header */
.page-header {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 16px 24px;
  margin-bottom: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  position: relative;
  z-index: 2;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-icon {
  font-size: 20px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.page-subtitle {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 400;
}

.header-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 120px;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 40px;
  height: 40px;
  top: 5px;
  right: 10px;
  animation-delay: 0s;
}

.circle-2 {
  width: 30px;
  height: 30px;
  top: 20px;
  right: 60px;
  animation-delay: 2s;
}

.circle-3 {
  width: 20px;
  height: 20px;
  top: 35px;
  right: 30px;
  animation-delay: 4s;
}

/* Data fetch controls */
.data-fetch-controls {
  margin-bottom: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

.fetch-controls-group {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-start;
}

.year-select {
  padding: 8px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.year-select:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.fetch-button, .save-button {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.fetch-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.save-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.fetch-button:hover, .save-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.fetch-button:disabled, .save-button:disabled {
  background: linear-gradient(135deg, #e0e0e0 0%, #f0f0f0 100%);
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Action buttons */
.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

.action-button {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-align: center;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.button-icon {
  font-size: 14px;
  display: inline-block;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-1px);
  }
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.action-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-button.import {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-button.match {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-button.tax {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-button.declaration {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.action-button.push {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.action-button.template {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.action-button.export-all {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
}

.action-button.template-download {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
}

.action-button.undo {
  background: linear-gradient(135deg, #fbc2eb 0%, #a6c1ee 100%);
  color: #333;
}

.action-button.undo:disabled {
  background: linear-gradient(135deg, #e0e0e0 0%, #f0f0f0 100%);
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Tabs */
.tabs-container {
  margin-bottom: 12px;
}

.tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 6px;
  overflow-x: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  gap: 3px;
}

.tabs::-webkit-scrollbar {
  height: 6px;
}

.tabs::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.tabs::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.tabs::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.tab {
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  border-radius: 8px;
  position: relative;
  background: transparent;
  min-width: 100px;
  text-align: center;
}

.tab:hover {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.tab.active {
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

/* Search */
.search-container {
  margin-bottom: 24px;
  position: relative;
}

.search-button {
  width: 100%;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

.search-button:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.search-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  z-index: 10;
  margin-top: 8px;
  padding: 24px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-group label {
  width: 100px;
  text-align: right;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-group input {
  flex: 1;
  padding: 10px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.form-group input:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.search-action {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.search-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.search-action.reset {
  background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%);
  color: #6b7280;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  text-align: center;
  color: white;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

/* VTable container */
.vtable-container {
  flex: 1;
  min-height: 0;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(20px);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.3s ease;
}

/* 确保隐藏的表格不占用空间但保持组件实例 */
.vtable-container > div[style*="display: none"] {
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
  visibility: hidden !important;
}

.vtable-container.loading {
  opacity: 0.7;
  pointer-events: none;
}

.vtable-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
}

/* Responsive design */
@media (max-width: 1200px) {
  .action-buttons {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .salary-tax-view {
    padding: 8px;
  }

  .page-header {
    padding: 12px 16px;
    margin-bottom: 8px;
  }

  .page-title {
    font-size: 18px;
  }

  .action-buttons {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 6px;
    padding: 8px;
    margin-bottom: 8px;
  }

  .action-button {
    padding: 8px 12px;
    font-size: 11px;
    min-height: 32px;
  }

  .tabs {
    padding: 4px;
    gap: 2px;
  }

  .tab {
    padding: 6px 12px;
    font-size: 11px;
    min-width: 80px;
  }

  .vtable-container {
    border-radius: 12px;
  }
}

@media (max-width: 480px) {
  .action-buttons {
    grid-template-columns: 1fr;
  }

  .tabs {
    flex-direction: column;
    align-items: stretch;
  }

  .tab {
    text-align: center;
    min-width: auto;
  }
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* Focus styles for accessibility */
.action-button:focus,
.tab:focus {
  outline: 2px solid rgba(102, 126, 234, 0.5);
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Print styles */
@media print {
  .salary-tax-view {
    background: white;
    padding: 0;
  }

  .page-header,
  .action-buttons,
  .tabs-container {
    display: none;
  }

  .vtable-container {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
</style>