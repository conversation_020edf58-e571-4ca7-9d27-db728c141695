/**
 * 测试fillRedTextToIncomeSheet函数的示例
 */
import { fillRedTextToIncomeSheet } from './fillCloseTamplate.js';
import { FUniver } from '@univerjs/presets';

/**
 * 测试函数使用示例
 * @param univerAPIInstance Univer API实例
 */
export function testFillRedTextFunction(univerAPIInstance: FUniver) {
    console.log('开始测试fillRedTextToIncomeSheet函数...');
    
    try {
        // 调用函数
        fillRedTextToIncomeSheet(univerAPIInstance);
        
        console.log('函数执行完成');
    } catch (error) {
        console.error('函数执行出错:', error);
    }
}

/**
 * 在Vue组件中的使用示例
 *
 * 在BudgetReportView.vue或其他组件中，可以这样使用：
 *
 * import { fillRedTextToIncomeSheet } from '@/scripts/fillCloseTamplate';
 *
 * // 在某个按钮点击事件或其他地方调用
 * const handleFillMissingData = () => {
 *   if (univerAPIInstance) {
 *     try {
 *       fillRedTextToIncomeSheet(univerAPIInstance);
 *       ElMessage.success('数据补全完成');
 *     } catch (error) {
 *       console.error('数据补全失败:', error);
 *       ElMessage.error('数据补全失败，请检查数据格式');
 *     }
 *   } else {
 *     console.error('Univer API实例未初始化');
 *     ElMessage.error('系统未初始化，请刷新页面重试');
 *   }
 * };
 *
 * // 在模板中添加按钮
 * // <el-button @click="handleFillMissingData" type="primary">
 * //   补全缺失数据
 * // </el-button>
 */

export default testFillRedTextFunction;
