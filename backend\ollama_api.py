from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import requests
import json

router = APIRouter()

class Message(BaseModel):
    message: str

@router.post("/chat")
async def chat_with_ollama(msg: Message):
    try:
        # Ollama API的地址，请根据你的实际部署情况修改
        ollama_url = "http://localhost:11434/api/generate"
        
        headers = {"Content-Type": "application/json"}
        data = {
            "model": "llama2", # 请替换为你实际使用的Ollama模型名称
            "prompt": msg.message,
            "stream": False
        }
        
        response = requests.post(ollama_url, headers=headers, data=json.dumps(data))
        response.raise_for_status() # 检查HTTP请求是否成功
        
        result = response.json()
        
        # 从Ollama的响应中提取'response'字段
        if 'response' in result:
            return {"ai_response": result["response"]}
        else:
            return {"ai_response": "未收到Ollama的有效回复。"}
            
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=500, detail=f"调用Ollama API失败: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {e}") 