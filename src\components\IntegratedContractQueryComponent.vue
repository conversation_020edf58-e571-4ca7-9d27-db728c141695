<template>
  <BaseQueryComponent
    title="一体化合同台账"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/integrated-contract'

// 查询字段配置
const queryFields = [
  {
    key: 'primaryKey',
    label: '查找主键',
    type: 'text',
    placeholder: '请输入主键',
    width: '200px'
  },
  {
    key: 'organizationName',
    label: '组织机构名称',
    type: 'text',
    placeholder: '请输入组织机构名称',
    width: '200px'
  },
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    placeholder: '请输入项目名称',
    width: '200px'
  },
  {
    key: 'projectCode',
    label: '项目编号',
    type: 'text',
    placeholder: '请输入项目编号',
    width: '180px'
  },
  {
    key: 'contractName',
    label: '合同名称',
    type: 'text',
    placeholder: '请输入合同名称',
    width: '200px'
  },
  {
    key: 'contractCode',
    label: '合同编号',
    type: 'text',
    placeholder: '请输入合同编号',
    width: '180px'
  },
  {
    key: 'originalContractCode',
    label: '原合同编号',
    type: 'text',
    placeholder: '请输入原合同编号',
    width: '180px'
  },
  {
    key: 'customerName',
    label: '客商名称',
    type: 'text',
    placeholder: '请输入客商名称',
    width: '200px'
  },
  {
    key: 'customerCode',
    label: '客商编号',
    type: 'text',
    placeholder: '请输入客商编号',
    width: '180px'
  },
  {
    key: 'contractType',
    label: '合同类型',
    type: 'select',
    placeholder: '请选择合同类型',
    width: '150px',
    options: [
      { label: '施工合同', value: '施工合同' },
      { label: '采购合同', value: '采购合同' },
      { label: '服务合同', value: '服务合同' },
      { label: '租赁合同', value: '租赁合同' }
    ]
  },
  {
    key: 'contractAmount',
    label: '合同金额',
    type: 'amount-range'
  },
  {
    key: 'taxRate',
    label: '税率',
    type: 'select',
    placeholder: '请选择税率',
    width: '120px',
    options: [
      { label: '3%', value: '3%' },
      { label: '6%', value: '6%' },
      { label: '9%', value: '9%' },
      { label: '13%', value: '13%' }
    ]
  },
  {
    key: 'settlementAmount',
    label: '结算金额',
    type: 'amount-range'
  },
  {
    key: 'prepaidAmount',
    label: '预付金额',
    type: 'amount-range'
  },
  {
    key: 'paidAmount',
    label: '已付金额',
    type: 'amount-range'
  },
  {
    key: 'invoiceAmount',
    label: '发票金额',
    type: 'amount-range'
  },
  {
    key: 'paymentRatio',
    label: '付款比例',
    type: 'number',
    placeholder: '请输入付款比例(%)',
    width: '150px'
  },
  {
    key: 'payableBalance',
    label: '应付余额',
    type: 'amount-range'
  },
  {
    key: 'overdueAmount',
    label: '拖欠款',
    type: 'amount-range'
  }
]
</script>
