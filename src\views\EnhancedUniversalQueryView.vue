<template>
  <div class="view-container">
    <div class="section-header">
      <h2>综合查询系统</h2>
      <p>多表联合查询与数据分析 - 增强版</p>
    </div>

    <!-- 版本控制和快照管理 -->
    <div class="version-control-panel">
      <div class="version-actions">
        <button @click="saveSnapshot" class="action-button save" :disabled="loading">
          <i class="icon-save"></i>保存快照
        </button>
        <button @click="showSnapshotDialog = true" class="action-button fetch" :disabled="loading">
          <i class="icon-load"></i>加载快照
        </button>
        <button @click="showVersionHistory = true" class="action-button export">
          <i class="icon-history"></i>版本历史
        </button>
        <button @click="exportToExcel" class="action-button export" :disabled="!hasData">
          <i class="icon-export"></i>导出Excel
        </button>
      </div>
      
      <div class="current-version" v-if="currentSnapshot">
        <span class="version-label">当前版本:</span>
        <span class="version-name">{{ currentSnapshot.name }}</span>
        <span class="version-time">{{ formatTime(currentSnapshot.created_at) }}</span>
      </div>
    </div>

    <!-- 查询表格 - 移除独立查询按钮，查询功能集成到表格中 -->
    <div class="query-table-container">
      <EnhancedVTableComponent
        ref="queryTable"
        :data="tableData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :enable-push-update="false"
        :show-query-panel="true"
        :available-tables="availableTables"
        class="enhanced-query-table"
        @data-change="handleDataChange"
        @query-execute="handleQueryExecute"
        @table-change="handleTableChange"
      />
    </div>

    <!-- 快照选择对话框 -->
    <div v-if="showSnapshotDialog" class="modal-overlay" @click="showSnapshotDialog = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>选择快照</h3>
          <button @click="showSnapshotDialog = false" class="close-button">&times;</button>
        </div>
        <div class="modal-body">
          <div v-if="snapshots.length === 0" class="no-snapshots">
            暂无保存的快照
          </div>
          <div v-else class="snapshots-list">
            <div
              v-for="snapshot in snapshots"
              :key="snapshot.id"
              class="snapshot-item"
              @click="loadSnapshotById(snapshot.id)"
            >
              <div class="snapshot-info">
                <div class="snapshot-name">{{ snapshot.name }}</div>
                <div class="snapshot-time">{{ formatTime(snapshot.created_at) }}</div>
                <div class="snapshot-remark" v-if="snapshot.remark">{{ snapshot.remark }}</div>
              </div>
              <button @click.stop="deleteSnapshot(snapshot.id)" class="delete-button">删除</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本历史对话框 -->
    <div v-if="showVersionHistory" class="modal-overlay" @click="showVersionHistory = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>版本历史</h3>
          <button @click="showVersionHistory = false" class="close-button">&times;</button>
        </div>
        <div class="modal-body">
          <div class="version-timeline">
            <div
              v-for="snapshot in snapshots"
              :key="snapshot.id"
              class="timeline-item"
            >
              <div class="timeline-marker"></div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="timeline-name">{{ snapshot.name }}</span>
                  <span class="timeline-time">{{ formatTime(snapshot.created_at) }}</span>
                </div>
                <div class="timeline-remark" v-if="snapshot.remark">{{ snapshot.remark }}</div>
                <div class="timeline-actions">
                  <button @click="loadSnapshotById(snapshot.id)" class="load-button">加载</button>
                  <button @click="deleteSnapshot(snapshot.id)" class="delete-button">删除</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 保存快照对话框 -->
    <div v-if="showSaveDialog" class="modal-overlay" @click="showSaveDialog = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>保存快照</h3>
          <button @click="showSaveDialog = false" class="close-button">&times;</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>备注信息:</label>
            <textarea
              v-model="snapshotRemark"
              placeholder="请输入快照备注信息（可选）"
              rows="3"
              class="remark-input"
            ></textarea>
          </div>
          <div class="form-actions">
            <button @click="confirmSaveSnapshot" class="confirm-button">确认保存</button>
            <button @click="showSaveDialog = false" class="cancel-button">取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, nextTick } from 'vue'
import EnhancedVTableComponent from '../components/EnhancedVTableComponent.vue'
import * as ExcelJS from 'exceljs'

// 响应式数据
const queryTable = ref(null)
const loading = ref(false)
const tableData = ref([])
const currentSnapshot = ref(null)
const snapshots = ref([])
const showSnapshotDialog = ref(false)
const showVersionHistory = ref(false)
const showSaveDialog = ref(false)
const snapshotRemark = ref('')

// 表格尺寸
const tableWidth = ref(1200)
const tableHeight = ref(600)

// 可用的数据表配置
const availableTables = ref([
  { label: '科目对照', value: 'subject_mapping' },
  { label: '异常数据', value: 'exception_data' },
  { label: '一体化合同台账', value: 'integrated_contract' },
  { label: '主数据', value: 'master_data' },
  { label: '付款台账', value: 'payment_ledger' },
  { label: '担保台账', value: 'guarantee_ledger' },
  { label: '内部银行', value: 'internal_bank' },
  { label: '内部对账', value: 'internal_reconciliation' }
])

// 计算属性
const hasData = computed(() => tableData.value.length > 1)

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN')
}

// 数据变化处理
const handleDataChange = (newData) => {
  tableData.value = newData
}

// 查询执行处理
const handleQueryExecute = async (queryParams) => {
  loading.value = true
  try {
    const response = await fetch('http://localhost:8000/api/universal-query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(queryParams)
    })

    if (!response.ok) {
      throw new Error(`查询失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success) {
      tableData.value = result.data
      console.log(`查询完成，共找到 ${result.data.length - 1} 条记录`)
    } else {
      throw new Error(result.message || '查询失败')
    }
  } catch (error) {
    console.error('查询失败:', error)
    // 如果API失败，可以显示错误信息或使用模拟数据
    alert('查询失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 表格变化处理
const handleTableChange = (tableName) => {
  console.log('表格切换到:', tableName)
}

// 保存快照
const saveSnapshot = () => {
  if (!hasData.value) {
    alert('没有数据可以保存')
    return
  }
  showSaveDialog.value = true
}

// 确认保存快照
const confirmSaveSnapshot = async () => {
  try {
    loading.value = true
    
    const snapshotData = {
      tableData: tableData.value,
      timestamp: new Date().toISOString()
    }

    const response = await fetch('http://localhost:8000/api/save-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([snapshotData, snapshotRemark.value])
    })

    if (!response.ok) {
      throw new Error(`保存失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success) {
      alert('快照保存成功!')
      showSaveDialog.value = false
      snapshotRemark.value = ''
      await loadSnapshotsList()
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存快照失败:', error)
    alert('保存快照失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载快照列表
const loadSnapshotsList = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/snapshots/list')
    
    if (!response.ok) {
      throw new Error(`获取快照列表失败: ${response.status}`)
    }

    snapshots.value = await response.json()
  } catch (error) {
    console.error('获取快照列表失败:', error)
  }
}

// 根据ID加载快照
const loadSnapshotById = async (snapshotId) => {
  try {
    loading.value = true
    
    const response = await fetch('http://localhost:8000/api/get-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ periodId: snapshotId })
    })

    if (!response.ok) {
      throw new Error(`加载快照失败: ${response.status}`)
    }

    const snapshotData = await response.json()
    
    if (snapshotData.tableData) {
      tableData.value = snapshotData.tableData
      currentSnapshot.value = snapshots.value.find(s => s.id === snapshotId)
      showSnapshotDialog.value = false
      showVersionHistory.value = false
      alert('快照加载成功!')
    }
  } catch (error) {
    console.error('加载快照失败:', error)
    alert('加载快照失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 删除快照
const deleteSnapshot = async (snapshotId) => {
  if (!confirm('确定要删除这个快照吗？此操作不可恢复！')) {
    return
  }

  try {
    const response = await fetch('http://localhost:8000/api/delete-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(snapshotId)
    })

    if (!response.ok) {
      throw new Error(`删除失败: ${response.status}`)
    }

    alert('快照删除成功!')
    await loadSnapshotsList()
  } catch (error) {
    console.error('删除快照失败:', error)
    alert('删除快照失败: ' + error.message)
  }
}

// 导出到Excel
const exportToExcel = async () => {
  if (!hasData.value) {
    alert('没有数据可以导出')
    return
  }

  try {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('查询结果')

    // 添加数据
    tableData.value.forEach((row, index) => {
      const excelRow = worksheet.addRow(row)
      
      // 设置表头样式
      if (index === 0) {
        excelRow.font = { bold: true }
        excelRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' }
        }
      }
    })

    // 自动调整列宽
    worksheet.columns.forEach(column => {
      let maxLength = 0
      column.eachCell({ includeEmpty: true }, cell => {
        const columnLength = cell.value ? cell.value.toString().length : 10
        if (columnLength > maxLength) {
          maxLength = columnLength
        }
      })
      column.width = Math.min(maxLength + 2, 50)
    })

    // 下载文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `综合查询结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('导出失败:', error)
    alert('导出失败: ' + error.message)
  }
}

// 组件挂载
onMounted(async () => {
  await loadSnapshotsList()

  // 尝试加载最新快照
  if (snapshots.value.length > 0) {
    await loadSnapshotById(snapshots.value[0].id)
  }
})
</script>

<style scoped>
.view-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
}

.section-header p {
  color: #7f8c8d;
  font-size: 16px;
}

/* 版本控制面板 */
.version-control-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-button.save {
  background: #27ae60;
  color: white;
}

.action-button.save:hover {
  background: #229954;
}

.action-button.fetch {
  background: #3498db;
  color: white;
}

.action-button.fetch:hover {
  background: #2980b9;
}

.action-button.export {
  background: #e67e22;
  color: white;
}

.action-button.export:hover {
  background: #d35400;
}

.action-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.current-version {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #2c3e50;
}

.version-label {
  font-weight: 600;
}

.version-name {
  background: #ecf0f1;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.version-time {
  color: #7f8c8d;
  font-size: 12px;
}

/* 查询表格容器 */
.query-table-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-query-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #e74c3c;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 快照列表 */
.no-snapshots {
  text-align: center;
  color: #7f8c8d;
  padding: 40px;
}

.snapshots-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.snapshot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.snapshot-item:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.snapshot-info {
  flex: 1;
}

.snapshot-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.snapshot-time {
  color: #7f8c8d;
  font-size: 12px;
  margin-bottom: 4px;
}

.snapshot-remark {
  color: #34495e;
  font-size: 14px;
}

.delete-button {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.delete-button:hover {
  background: #c0392b;
}

/* 版本时间线 */
.version-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 24px;
  bottom: -24px;
  width: 2px;
  background: #ecf0f1;
}

.timeline-item:last-child::before {
  display: none;
}

.timeline-marker {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
  margin-right: 16px;
  margin-top: 4px;
  flex-shrink: 0;
}

.timeline-content {
  flex: 1;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-name {
  font-weight: 600;
  color: #2c3e50;
}

.timeline-time {
  color: #7f8c8d;
  font-size: 12px;
}

.timeline-remark {
  color: #34495e;
  margin-bottom: 12px;
  font-size: 14px;
}

.timeline-actions {
  display: flex;
  gap: 8px;
}

.load-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.load-button:hover {
  background: #2980b9;
}

/* 保存对话框 */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.remark-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
}

.remark-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.confirm-button {
  background: #27ae60;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.confirm-button:hover {
  background: #229954;
}

.cancel-button {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button:hover {
  background: #7f8c8d;
}

/* 图标样式 */
.icon-save::before { content: '💾'; }
.icon-load::before { content: '📂'; }
.icon-history::before { content: '📋'; }
.icon-export::before { content: '📤'; }
</style>
