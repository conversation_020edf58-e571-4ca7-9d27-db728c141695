<template>
  <div class="vtable-copy-paste-test">
    <h1>VTable 复制粘贴功能测试</h1>
    
    <div class="test-info">
      <h3>🧪 测试说明</h3>
      <p>这个页面用于测试和修复VTable组件的复制粘贴功能</p>
      <ul>
        <li>测试单个值复制粘贴是否会跨行</li>
        <li>测试多个值复制粘贴是否正常</li>
        <li>测试键盘快捷键 Ctrl+C 和 Ctrl+V</li>
        <li>测试按钮复制粘贴功能</li>
      </ul>
    </div>

    <div class="test-controls">
      <h3>🎮 测试工具</h3>
      <button @click="copyTestValue" class="test-btn">复制单个测试值</button>
      <button @click="copyMultipleValues" class="test-btn">复制多个值</button>
      <button @click="clearTable" class="test-btn warning">清空表格</button>
      <button @click="resetTable" class="test-btn">重置表格</button>
      <button @click="printTableData" class="test-btn info">打印表格数据</button>
    </div>

    <div class="test-table">
      <h3>📊 测试表格</h3>
      <p>请在下面的表格中测试复制粘贴功能：</p>
      
      <VTableComponent
        ref="testTableRef"
        :data="testData"
        :width="800"
        :height="400"
        :show-filter="false"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="handleDataChange"
        @paste="handlePasteEvent"
        @copy="handleCopyEvent"
        @cell-edit="handleCellEdit"
      />
    </div>

    <div class="test-logs">
      <h3>📝 测试日志</h3>
      <div class="log-controls">
        <button @click="clearLogs" class="test-btn">清空日志</button>
        <button @click="exportLogs" class="test-btn">导出日志</button>
      </div>
      <div class="log-container" ref="logContainer">
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          :class="['log-entry', log.type]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 测试表格引用
const testTableRef = ref(null)
const logContainer = ref(null)

// 测试数据
const testData = ref([
  ['姓名', '年龄', '部门', '工资', '备注'],
  ['张三', '25', '技术部', '8000', ''],
  ['李四', '30', '销售部', '7000', ''],
  ['王五', '28', '人事部', '6500', ''],
  ['赵六', '32', '财务部', '7500', ''],
  ['', '', '', '', ''],
  ['', '', '', '', ''],
  ['', '', '', '', '']
])

// 日志记录
const logs = ref([])

// 添加日志
const addLog = (message, type = 'info') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  logs.value.push({
    time,
    message,
    type
  })
  
  // 自动滚动到底部
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight
    }
  })
}

// 处理数据变化
const handleDataChange = (newData) => {
  testData.value = newData
  addLog(`表格数据已更新，共 ${newData.length} 行`, 'success')
}

// 处理粘贴事件
const handlePasteEvent = (args) => {
  addLog(`粘贴事件触发: ${JSON.stringify(args)}`, 'paste')
}

// 处理复制事件
const handleCopyEvent = (args) => {
  addLog(`复制事件触发: ${JSON.stringify(args)}`, 'copy')
}

// 处理单元格编辑
const handleCellEdit = (args) => {
  addLog(`单元格编辑: 行${args.row} 列${args.col} 新值: ${args.newValue}`, 'edit')
}

// 复制单个测试值
const copyTestValue = async () => {
  const testValue = '测试单个值'
  try {
    await navigator.clipboard.writeText(testValue)
    addLog(`已复制单个测试值到剪贴板: "${testValue}"`, 'copy')
  } catch (error) {
    addLog(`复制失败: ${error.message}`, 'error')
  }
}

// 复制多个值
const copyMultipleValues = async () => {
  const multipleValues = '值1\t值2\t值3\n值4\t值5\t值6'
  try {
    await navigator.clipboard.writeText(multipleValues)
    addLog('已复制多个值到剪贴板 (2行3列)', 'copy')
  } catch (error) {
    addLog(`复制失败: ${error.message}`, 'error')
  }
}

// 清空表格
const clearTable = () => {
  testData.value = [
    ['姓名', '年龄', '部门', '工资', '备注'],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', '']
  ]
  addLog('表格已清空', 'warning')
}

// 重置表格
const resetTable = () => {
  testData.value = [
    ['姓名', '年龄', '部门', '工资', '备注'],
    ['张三', '25', '技术部', '8000', ''],
    ['李四', '30', '销售部', '7000', ''],
    ['王五', '28', '人事部', '6500', ''],
    ['赵六', '32', '财务部', '7500', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', '']
  ]
  addLog('表格已重置为初始数据', 'success')
}

// 打印表格数据
const printTableData = () => {
  console.log('当前表格数据:', testData.value)
  addLog('表格数据已打印到控制台', 'info')
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 导出日志
const exportLogs = () => {
  const logText = logs.value.map(log => `[${log.time}] ${log.message}`).join('\n')
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `vtable-test-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  a.click()
  URL.revokeObjectURL(url)
  addLog('日志已导出', 'success')
}

onMounted(() => {
  addLog('VTable 复制粘贴测试页面已加载', 'success')
})
</script>

<style scoped>
.vtable-copy-paste-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-info h3 {
  margin-top: 0;
  color: #333;
}

.test-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-controls h3 {
  margin-bottom: 10px;
}

.test-btn {
  padding: 8px 16px;
  margin: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.test-btn {
  background: #007bff;
  color: white;
}

.test-btn:hover {
  background: #0056b3;
}

.test-btn.warning {
  background: #ffc107;
  color: #212529;
}

.test-btn.warning:hover {
  background: #e0a800;
}

.test-btn.info {
  background: #17a2b8;
  color: white;
}

.test-btn.info:hover {
  background: #138496;
}

.test-table {
  margin-bottom: 20px;
}

.test-table h3 {
  margin-bottom: 10px;
}

.test-logs {
  margin-top: 20px;
}

.log-controls {
  margin-bottom: 10px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background: #f9f9f9;
  font-family: monospace;
  font-size: 12px;
}

.log-entry {
  margin-bottom: 5px;
  padding: 2px 0;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-entry.success {
  color: #28a745;
}

.log-entry.error {
  color: #dc3545;
}

.log-entry.warning {
  color: #ffc107;
}

.log-entry.copy {
  color: #007bff;
}

.log-entry.paste {
  color: #6f42c1;
}

.log-entry.edit {
  color: #fd7e14;
}
</style>
