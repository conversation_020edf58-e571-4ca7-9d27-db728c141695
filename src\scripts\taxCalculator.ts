export default async function taxCalculatorTs(taxCalculationData,specialDeductionData){
    const header = taxCalculationData[0];
    const dataRows = taxCalculationData.slice(1);

    const incomeIndex = header.indexOf('本期收入');
    const taxIndex = header.indexOf('本次税款');

    if (incomeIndex === -1 || taxIndex === -1) {
      alert('算税底稿表头不正确，无法计算个税！');
      return;
    }
    //决定将计算方式放在后端
    const response = await fetch('http://localhost:8000/api/calculate-tax', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taxCalculationData,
        specialDeductionData
      })
    });

    if (!response.ok) {
      throw new Error(`计算个税失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return result;


}




