# Tab 切换数据持久化解决方案

## 问题描述

在使用多个 `v-if` 条件渲染不同的 VTableComponent 时，切换 tab 会导致：
1. 组件被销毁和重新创建
2. 用户在表格中的修改数据丢失
3. 数据被重置为 props.data 的原始值

## 解决方案

### 1. 使用 v-show 替代 v-if

**之前的问题代码：**
```vue
<VTableComponent
  v-if="activeTabIndex === 0"
  :data="taxDeclarationData"
  @data-change="handleDataChange"
/>
<VTableComponent
  v-else-if="activeTabIndex === 1"
  :data="taxCalculationData"
  @data-change="handleDataChange"
/>
```

**修改后的解决方案：**
```vue
<VTableComponent
  v-show="activeTabIndex === 0"
  ref="taxDeclarationTable"
  :key="'tab-0'"
  :data="taxDeclarationData"
  @data-change="(newData) => handleDataChange(newData, 0)"
/>
<VTableComponent
  v-show="activeTabIndex === 1"
  ref="taxCalculationTable"
  :key="'tab-1'"
  :data="taxCalculationData"
  @data-change="(newData) => handleDataChange(newData, 1)"
/>
```

### 2. 数据缓存机制

```javascript
// 数据缓存机制 - 用于保存每个表格的实时修改状态
const dataCache = ref({
  0: null, // taxDeclarationData
  1: null, // taxCalculationData
  // ... 其他表格
});

const handleDataChange = (newData, tabIndex = null) => {
  const targetTabIndex = tabIndex !== null ? tabIndex : activeTabIndex.value;
  const activeData = getActiveTableData(targetTabIndex);
  if (activeData) {
    // 更新主数据
    activeData.value = newData;
    // 同时更新缓存
    dataCache.value[targetTabIndex] = JSON.parse(JSON.stringify(newData));
  }
};
```

### 3. 表格数据刷新机制

```javascript
// 刷新指定表格的数据显示
const refreshTableData = (tabIndex) => {
  const tableRef = getTableRefByIndex(tabIndex);
  const tableData = getActiveTableData(tabIndex);
  
  if (tableRef && tableData && tableRef.setData) {
    // 使用 VTableComponent 的 setData 方法强制刷新
    tableRef.setData(tableData.value);
    console.log(`Refreshed table data for tab ${tabIndex}`);
  }
};
```

### 4. 直接修改数据后的刷新

当直接修改 `.value` 时，需要手动刷新表格：

```javascript
// 示例：匹配身份证号及项目
const matchIdAndProject = () => {
  // ... 数据修改逻辑
  taxCalculationData.value.slice(1).forEach(row => {
    // 修改数据
    row[idIndex] = idMap.get(name);
  });
  
  // 重要：修改后刷新表格显示
  refreshTableData(1);
  
  // ... 其他逻辑
};
```

### 5. 切换 Tab 时的处理

```javascript
const switchTab = (index) => {
  activeTabIndex.value = index;
  
  // 如果有缓存数据，恢复它
  if (dataCache.value[index]) {
    const activeData = getActiveTableData(index);
    if (activeData) {
      activeData.value = JSON.parse(JSON.stringify(dataCache.value[index]));
    }
  }
  
  // 刷新表格尺寸
  nextTick(() => {
    const currentTableRef = getCurrentTableRef();
    if (currentTableRef && currentTableRef.getTableInstance) {
      const tableInstance = currentTableRef.getTableInstance();
      if (tableInstance) {
        tableInstance.resize();
        tableInstance.updateScrollBar();
      }
    }
  });
};
```

## 关键改进点

### ✅ 优势
1. **数据持久性**：修改的数据在切换 tab 时不会丢失
2. **性能优化**：避免了组件的重复创建和销毁
3. **用户体验**：保持编辑状态和滚动位置
4. **向后兼容**：不影响现有的功能和 API

### ⚠️ 注意事项
1. **内存使用**：所有表格组件都会保持在内存中
2. **手动刷新**：直接修改 `.value` 后需要调用 `refreshTableData()`
3. **CSS 隐藏**：使用 CSS 确保隐藏的表格不影响布局

## 测试方法

访问 `/tab-switch-test` 页面进行测试：
1. 在表格中编辑数据
2. 切换到其他 tab
3. 切换回来验证数据是否保持
4. 使用"测试直接修改数据"按钮测试 `.value` 修改场景

## 应用场景

这个解决方案适用于：
- 多 tab 表格编辑界面
- 需要保持用户编辑状态的场景
- 复杂的数据处理流程
- 需要在不同视图间切换但保持数据的应用
