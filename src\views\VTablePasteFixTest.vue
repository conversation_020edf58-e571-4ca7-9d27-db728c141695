<template>
  <div class="paste-fix-test-view">
    <h1>VTable 复制粘贴修复测试</h1>
    
    <div class="test-info">
      <div class="bug-description">
        <h3>🐛 原始问题</h3>
        <p>复制粘贴有些bug，粘贴单个时好像会跨行</p>
      </div>
      
      <div class="fix-description">
        <h3>🔧 修复内容</h3>
        <ul>
          <li>优化了键盘选项配置，防止意外的焦点移动</li>
          <li>增强了粘贴事件处理，添加数据类型检测</li>
          <li>重写了粘贴方法，特殊处理单个值粘贴</li>
          <li>添加了 Ctrl+V 键盘事件拦截</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h2>测试表格</h2>
      <p>请按照以下步骤测试复制粘贴功能：</p>
      
      <div class="test-steps">
        <h4>测试步骤：</h4>
        <ol>
          <li><strong>单个值粘贴测试：</strong>
            <ul>
              <li>在任意地方复制一个单个文本（如：<code>测试值</code>）</li>
              <li>在下面表格中选中一个单元格</li>
              <li>按 <code>Ctrl+V</code> 或点击"粘贴"按钮</li>
              <li>检查值是否只出现在选中的单元格中</li>
            </ul>
          </li>
          <li><strong>多值粘贴测试：</strong>
            <ul>
              <li>在 Excel 或其他表格中复制多行多列数据</li>
              <li>在下面表格中选中起始位置</li>
              <li>按 <code>Ctrl+V</code> 粘贴</li>
              <li>检查数据是否按原始结构粘贴</li>
            </ul>
          </li>
        </ol>
      </div>

      <div class="table-wrapper">
        <VTableComponent
          ref="vtableRef"
          :data="testData"
          :width="800"
          :height="400"
          :show-filter="true"
          :editable="true"
          :enable-copy-paste="true"
          :auto-width="true"
          @data-change="handleDataChange"
          @paste="handlePasteEvent"
        />
      </div>
    </div>

    <div class="test-section">
      <h2>测试工具</h2>
      <div class="test-tools">
        <button @click="copyTestValue" class="test-btn">复制测试值</button>
        <button @click="copyMultipleValues" class="test-btn">复制多个值</button>
        <button @click="clearTable" class="test-btn danger">清空表格</button>
        <button @click="resetTable" class="test-btn">重置表格</button>
      </div>
    </div>

    <div class="test-section">
      <h2>调试信息</h2>
      <div class="debug-info">
        <h4>粘贴事件日志：</h4>
        <div class="log-container">
          <div v-for="(log, index) in pasteLog" :key="index" class="log-entry">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <button @click="clearLog" class="test-btn">清空日志</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'

const vtableRef = ref()
const pasteLog = ref([])

// 测试数据
const initialData = [
  ['姓名', '部门', '职位', '邮箱', '电话', '备注'],
  ['张三', '技术部', '工程师', '<EMAIL>', '13800138001', ''],
  ['李四', '产品部', '经理', '<EMAIL>', '13800138002', ''],
  ['王五', '设计部', '设计师', '<EMAIL>', '13800138003', ''],
  ['赵六', '市场部', '专员', '<EMAIL>', '13800138004', ''],
  ['', '', '', '', '', ''],
  ['', '', '', '', '', ''],
  ['', '', '', '', '', ''],
]

const testData = ref([...initialData])

// 添加日志
const addLog = (message) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  pasteLog.value.unshift({ time, message })
  
  // 限制日志数量
  if (pasteLog.value.length > 20) {
    pasteLog.value = pasteLog.value.slice(0, 20)
  }
}

// 处理数据变化
const handleDataChange = (newData) => {
  addLog(`数据已更新，共 ${newData.length} 行`)
}

// 处理粘贴事件
const handlePasteEvent = (args) => {
  addLog(`粘贴事件触发：${JSON.stringify(args)}`)
}

// 复制测试值
const copyTestValue = async () => {
  const testValue = '测试单个值'
  try {
    await navigator.clipboard.writeText(testValue)
    addLog(`已复制测试值到剪贴板：${testValue}`)
  } catch (error) {
    addLog(`复制失败：${error.message}`)
  }
}

// 复制多个值
const copyMultipleValues = async () => {
  const multipleValues = '值1\t值2\t值3\n值4\t值5\t值6'
  try {
    await navigator.clipboard.writeText(multipleValues)
    addLog('已复制多个值到剪贴板（2行3列）')
  } catch (error) {
    addLog(`复制失败：${error.message}`)
  }
}

// 清空表格
const clearTable = () => {
  testData.value = [testData.value[0], ...Array(7).fill(['', '', '', '', '', ''])]
  addLog('表格已清空')
}

// 重置表格
const resetTable = () => {
  testData.value = [...initialData]
  addLog('表格已重置')
}

// 清空日志
const clearLog = () => {
  pasteLog.value = []
}

onMounted(() => {
  addLog('测试页面已加载，可以开始测试复制粘贴功能')
})
</script>

<style scoped>
.paste-fix-test-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.bug-description, .fix-description {
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
}

.bug-description {
  background: #fff3cd;
  border-left-color: #ffc107;
}

.fix-description {
  background: #d1ecf1;
  border-left-color: #17a2b8;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.test-steps {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.test-steps code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.table-wrapper {
  margin: 20px 0;
}

.test-tools {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.test-btn:not(.danger) {
  background: #007bff;
  color: white;
}

.test-btn:not(.danger):hover {
  background: #0056b3;
}

.test-btn.danger {
  background: #dc3545;
  color: white;
}

.test-btn.danger:hover {
  background: #c82333;
}

.debug-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
}

.log-entry {
  display: block;
  padding: 4px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
}

.log-message {
  color: #333;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

h2 {
  color: #495057;
  margin-bottom: 15px;
}

h3 {
  margin-top: 0;
  color: #333;
}
</style>
