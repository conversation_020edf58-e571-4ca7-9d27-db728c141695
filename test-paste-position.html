<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTable 粘贴位置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .problem-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .solution-section {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .test-steps {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 4px;
            margin: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .expected-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .actual-result {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 VTable 粘贴位置问题测试</h1>
        
        <div class="problem-section">
            <h3>🐛 当前问题</h3>
            <p><strong>用户反馈：</strong>"现在变成了粘贴不是本单元格成功，而是粘贴到了下一个位置"</p>
            
            <h4>问题现象：</h4>
            <ul>
                <li>选中某个单元格（如：行2列3）</li>
                <li>粘贴一个值</li>
                <li>值没有粘贴到选中的单元格</li>
                <li>而是粘贴到了下一个位置（如：行3列3）</li>
            </ul>
        </div>

        <div class="solution-section">
            <h3>🔧 修复方案</h3>
            <p><strong>问题分析：</strong>VTable的行索引可能包含表头行，导致索引偏移</p>
            
            <h4>修复内容：</h4>
            <ul>
                <li>添加详细的索引调试信息</li>
                <li>检查VTable返回的行索引是否包含表头</li>
                <li>如果包含表头，则将行索引减1</li>
                <li>确保粘贴到正确的数据行</li>
            </ul>
            
            <div class="code">
// 修复代码
let { row, col } = range.start;
console.log(`📝 原始索引: 行${row} 列${col}`);

// VTable可能包含表头行，所以数据行索引需要减1
if (row > 0 && row <= records.value.length) {
  row = row - 1; // 调整为数据行索引
  console.log(`🔧 调整后的行索引: ${row}`);
}
            </div>
        </div>

        <div class="test-steps">
            <h3>🧪 测试步骤</h3>
            
            <h4>准备工作：</h4>
            <ol>
                <li>打开薪酬税务视图</li>
                <li>切换到"算税底稿"标签页</li>
                <li>打开浏览器开发者工具（F12）</li>
                <li>切换到Console标签页</li>
            </ol>

            <h4>测试流程：</h4>
            <ol>
                <li><strong>复制测试值：</strong>
                    <button class="btn" onclick="copyTestValue('999')">复制测试值: 999</button>
                </li>
                <li><strong>选择目标单元格：</strong>在"失业保险费"列选择一个有数据的单元格（如：80）</li>
                <li><strong>记录位置：</strong>注意选中单元格的行列位置</li>
                <li><strong>执行粘贴：</strong>按 Ctrl+V 或点击粘贴按钮</li>
                <li><strong>检查结果：</strong>查看值是否粘贴到了正确的位置</li>
                <li><strong>查看日志：</strong>在控制台查看详细的调试信息</li>
            </ol>

            <div class="expected-result">
                <h4>✅ 预期结果：</h4>
                <ul>
                    <li>值"999"出现在选中的单元格中</li>
                    <li>其他单元格的值保持不变</li>
                    <li>控制台显示正确的索引调整信息</li>
                </ul>
            </div>

            <div class="actual-result">
                <h4>❌ 如果仍有问题：</h4>
                <ul>
                    <li>值出现在错误的位置</li>
                    <li>控制台显示索引偏移信息</li>
                    <li>需要进一步调整索引计算</li>
                </ul>
            </div>
        </div>

        <div class="test-steps">
            <h3>🔍 调试信息解读</h3>
            
            <h4>关键日志信息：</h4>
            <div class="code">
🎯 选择单元格: {col: X, row: Y}
📝 原始索引: 行Y 列X 值"999"
🔧 调整后的行索引: Y-1 (减去表头行)
📝 最终使用索引: 行Y-1 列X 值"999"
🔄 使用直接数据更新: 字段"fieldName"
✅ 直接数据更新成功
            </div>

            <h4>索引对应关系：</h4>
            <ul>
                <li><strong>VTable行索引：</strong>包含表头的行号（从0开始，0是表头）</li>
                <li><strong>数据行索引：</strong>实际数据在records数组中的索引（从0开始）</li>
                <li><strong>转换公式：</strong>数据行索引 = VTable行索引 - 1</li>
            </ul>
        </div>

        <div class="test-steps">
            <h3>🚀 快速测试</h3>
            <p>点击下面的按钮快速开始测试：</p>
            
            <a href="http://localhost:3001/salary-tax" target="_blank" class="btn success">
                打开薪酬税务视图
            </a>
            
            <button class="btn" onclick="copyTestValue('123')">复制: 123</button>
            <button class="btn" onclick="copyTestValue('456')">复制: 456</button>
            <button class="btn" onclick="copyTestValue('789')">复制: 789</button>
            
            <button class="btn warning" onclick="clearClipboard()">清空剪贴板</button>
        </div>
    </div>

    <script>
        // 复制测试值
        async function copyTestValue(value) {
            try {
                await navigator.clipboard.writeText(value);
                alert(`✅ 已复制测试值: "${value}"`);
            } catch (error) {
                alert(`❌ 复制失败: ${error.message}`);
            }
        }

        // 清空剪贴板
        async function clearClipboard() {
            try {
                await navigator.clipboard.writeText('');
                alert('✅ 剪贴板已清空');
            } catch (error) {
                alert(`❌ 清空失败: ${error.message}`);
            }
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 VTable粘贴位置测试工具已准备就绪');
            console.log('📋 请按照测试步骤进行验证');
        });
    </script>
</body>
</html>
