<template>
  <div class="demo-container">
    <h2>专项附加扣除导入功能演示</h2>
    
    <!-- 功能说明 -->
    <div class="info-panel">
      <h3>功能说明</h3>
      <ul>
        <li>📥 <strong>导入Excel</strong>: 支持导入专项附加扣除Excel文件，自动追加到现有数据后面</li>
        <li>📋 <strong>下载模板</strong>: 提供标准的Excel导入模板，确保数据格式正确</li>
        <li>↩️ <strong>撤销功能</strong>: 支持撤销最近的操作，最多保存10步历史记录</li>
        <li>🗑️ <strong>删除行</strong>: 选中行后可以批量删除</li>
        <li>➕ <strong>添加行</strong>: 快速添加新的数据行</li>
        <li>📊 <strong>导出Excel</strong>: 将当前数据导出为Excel文件</li>
      </ul>
    </div>
    
    <!-- 操作按钮 -->
    <div class="action-panel">
      <button @click="downloadTemplate" class="demo-btn template">
        📋 下载导入模板
      </button>
      <button @click="importExcel" class="demo-btn import">
        📥 导入Excel文件
      </button>
      <button @click="exportExcel" class="demo-btn export">
        📊 导出Excel
      </button>
      <button @click="undoAction" class="demo-btn undo" :disabled="undoStack.length === 0">
        ↩️ 撤销操作 ({{ undoStack.length }})
      </button>
      <button @click="addSampleData" class="demo-btn add">
        ➕ 添加示例数据
      </button>
      <button @click="clearData" class="demo-btn clear">
        🗑️ 清空数据
      </button>
    </div>
    
    <!-- 数据统计 -->
    <div class="stats-panel">
      <div class="stat-item">
        <span class="stat-label">总行数:</span>
        <span class="stat-value">{{ tableData.length - 1 }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">撤销步数:</span>
        <span class="stat-value">{{ undoStack.length }}/10</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">最后更新:</span>
        <span class="stat-value">{{ lastUpdate }}</span>
      </div>
    </div>
    
    <!-- VTable组件 -->
    <VTableComponent
      ref="vtableRef"
      :data="tableData"
      :width="1000"
      :height="400"
      :show-filter="true"
      :editable="true"
      :enable-copy-paste="true"
      :auto-width="true"
      @data-change="handleDataChange"
      @cell-edit="handleCellEdit"
    />
    
    <!-- 使用说明 -->
    <div class="usage-panel">
      <h3>使用说明</h3>
      <ol>
        <li><strong>下载模板</strong>: 点击"下载导入模板"按钮获取标准Excel模板</li>
        <li><strong>填写数据</strong>: 在模板中填写专项附加扣除数据，保持列结构不变</li>
        <li><strong>导入数据</strong>: 点击"导入Excel文件"选择填写好的Excel文件</li>
        <li><strong>验证数据</strong>: 系统会自动验证数据格式，格式正确则追加到表格末尾</li>
        <li><strong>撤销操作</strong>: 如果导入有误，可以点击"撤销操作"恢复到导入前状态</li>
        <li><strong>编辑数据</strong>: 双击单元格可以直接编辑数据</li>
        <li><strong>导出数据</strong>: 完成编辑后可以导出为Excel文件保存</li>
      </ol>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'
import * as ExcelJS from 'exceljs'

// 表格数据
const tableData = ref([
  [
    '工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止',
    '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息',
    '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'
  ],
  [
    'E001', '张三', '身份证', '110101199001011234', '2025-01-01', '2025-01-31',
    15000, 1000, 0, 1000, 0, 2000, 0, 0
  ],
  [
    'E002', '李四', '身份证', '110101199102022345', '2025-01-01', '2025-01-31',
    12000, 1000, 400, 0, 1500, 2000, 0, 0
  ]
])

const vtableRef = ref(null)
const undoStack = ref([])
const maxUndoSteps = 10
const lastUpdate = ref(new Date().toLocaleString())

// 保存状态到撤销栈
const saveToUndoStack = () => {
  const currentState = {
    timestamp: new Date().toISOString(),
    data: JSON.parse(JSON.stringify(tableData.value))
  }
  
  undoStack.value.push(currentState)
  
  if (undoStack.value.length > maxUndoSteps) {
    undoStack.value.shift()
  }
}

// 下载模板
const downloadTemplate = async () => {
  try {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('专项附加扣除模板')
    
    const headers = tableData.value[0]
    
    // 设置表头
    const headerRow = worksheet.addRow(headers)
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      }
      cell.alignment = { horizontal: 'center', vertical: 'middle' }
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    })
    
    // 添加示例数据
    const exampleRow = [
      'E999', '示例员工', '身份证', '110101199001011234', '2025-01-01', '2025-01-31',
      15000, 1000, 0, 1000, 0, 2000, 0, 0
    ]
    worksheet.addRow(exampleRow)
    
    // 自动调整列宽
    worksheet.columns.forEach((column, index) => {
      const maxLength = Math.max(
        headers[index]?.length || 10,
        String(exampleRow[index] || '').length
      )
      column.width = Math.min(Math.max(maxLength + 2, 10), 30)
    })
    
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.href = url
    link.download = `专项附加扣除导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    URL.revokeObjectURL(url)
    
    alert('模板下载成功！')
  } catch (error) {
    console.error('下载模板失败:', error)
    alert('下载模板失败: ' + error.message)
  }
}

// 导入Excel
const importExcel = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  
  input.onchange = async (event) => {
    const file = event.target.files[0]
    if (!file) return
    
    try {
      saveToUndoStack()
      
      const workbook = new ExcelJS.Workbook()
      const arrayBuffer = await file.arrayBuffer()
      await workbook.xlsx.load(arrayBuffer)
      
      const worksheet = workbook.getWorksheet(1)
      const data = []
      
      worksheet.eachRow((row, rowNumber) => {
        const rowData = []
        row.eachCell({ includeEmpty: true }, (cell) => {
          let value = cell.value
          if (value && typeof value === 'object') {
            if (value.formula) {
              value = value.result || ''
            } else if (value.text) {
              value = value.text
            } else {
              value = String(value)
            }
          }
          rowData.push(value || '')
        })
        
        if (rowData.some(cell => cell !== '')) {
          data.push(rowData)
        }
      })
      
      if (data.length > 1) {
        // 跳过标题行，追加数据
        const dataRows = data.slice(1)
        tableData.value.push(...dataRows)
        lastUpdate.value = new Date().toLocaleString()
        alert(`成功导入 ${dataRows.length} 条数据`)
      } else {
        alert('Excel文件为空或格式不正确')
      }
    } catch (error) {
      console.error('导入失败:', error)
      alert('导入失败: ' + error.message)
    }
  }
  
  input.click()
}

// 导出Excel
const exportExcel = () => {
  if (vtableRef.value) {
    vtableRef.value.exportExcel()
  }
}

// 撤销操作
const undoAction = () => {
  if (undoStack.value.length === 0) {
    alert('没有可撤销的操作')
    return
  }
  
  const lastState = undoStack.value.pop()
  tableData.value = lastState.data
  lastUpdate.value = new Date().toLocaleString()
  alert('操作已撤销')
}

// 添加示例数据
const addSampleData = () => {
  saveToUndoStack()
  
  const sampleData = [
    ['E003', '王五', '身份证', '110101199203033456', '2025-01-01', '2025-01-31', 18000, 2000, 0, 1000, 0, 2000, 1000, 0],
    ['E004', '赵六', '身份证', '110101199304044567', '2025-01-01', '2025-01-31', 20000, 1000, 800, 1000, 0, 2000, 0, 500]
  ]
  
  tableData.value.push(...sampleData)
  lastUpdate.value = new Date().toLocaleString()
}

// 清空数据
const clearData = () => {
  if (confirm('确定要清空所有数据吗？')) {
    saveToUndoStack()
    tableData.value = [tableData.value[0]] // 只保留表头
    lastUpdate.value = new Date().toLocaleString()
  }
}

// 处理数据变化
const handleDataChange = (newData) => {
  lastUpdate.value = new Date().toLocaleString()
}

// 处理单元格编辑
const handleCellEdit = (editInfo) => {
  lastUpdate.value = new Date().toLocaleString()
}
</script>

<style scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-panel, .usage-panel {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #409eff;
}

.info-panel ul, .usage-panel ol {
  margin: 10px 0;
  padding-left: 20px;
}

.info-panel li, .usage-panel li {
  margin: 8px 0;
  line-height: 1.5;
}

.action-panel {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
  color: white;
}

.demo-btn.template { background-color: #722ed1; }
.demo-btn.import { background-color: #1890ff; }
.demo-btn.export { background-color: #52c41a; }
.demo-btn.undo { background-color: #fa8c16; }
.demo-btn.add { background-color: #13c2c2; }
.demo-btn.clear { background-color: #f5222d; }

.demo-btn:hover:not(:disabled) {
  opacity: 0.85;
  transform: translateY(-1px);
}

.demo-btn:disabled {
  background-color: #d9d9d9;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

.stats-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
}
</style>
