<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理工具测试 - 表名选择功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .test-result.success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-result.info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-result.warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .api-endpoint {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
        .table-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .table-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: white;
        }
        .table-card h4 {
            margin-top: 0;
            color: #007bff;
        }
        .field-list {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ 数据库管理工具测试 - 表名选择功能</h1>
        
        <div class="test-section">
            <h2>📋 功能概述</h2>
            <div class="test-result success">
                <strong>✨ 新增功能</strong>: 数据库管理工具现在支持表名选择功能
                <ul class="feature-list">
                    <li>表名下拉选择器（科目对照、异常数据）</li>
                    <li>基于表名的动态查询</li>
                    <li>基于表名的数据更新</li>
                    <li>表特定的示例数据</li>
                    <li>改进的用户界面</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 API 接口更新</h2>
            <div class="test-result info">
                <strong>查询接口</strong>:
                <div class="api-endpoint">
                    GET /api/db-admin/query?table_name={table_name}
                </div>
                <p>支持的表名: <code>subject_mapping</code> (科目对照), <code>exception_data</code> (异常数据)</p>
            </div>
            
            <div class="test-result info">
                <strong>更新接口</strong>:
                <div class="api-endpoint">
                    POST /api/db-admin/update<br>
                    Body: {<br>
                    &nbsp;&nbsp;"table_name": "subject_mapping",<br>
                    &nbsp;&nbsp;"data": [["列1","列2"], ["值1","值2"]]<br>
                    }
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 支持的数据表</h2>
            <div class="table-info">
                <div class="table-card">
                    <h4>科目对照表 (subject_mapping)</h4>
                    <div class="field-list">
                        <strong>字段:</strong>
                        <ul>
                            <li>id - 主键ID</li>
                            <li>subject_code - 科目代码</li>
                            <li>subject_name - 科目名称</li>
                            <li>mapping_code - 映射代码</li>
                            <li>mapping_name - 映射名称</li>
                            <li>category - 科目类别</li>
                            <li>status - 状态</li>
                            <li>created_date - 创建日期</li>
                            <li>updated_date - 更新日期</li>
                            <li>remark - 备注</li>
                        </ul>
                    </div>
                </div>
                
                <div class="table-card">
                    <h4>异常数据表 (exception_data)</h4>
                    <div class="field-list">
                        <strong>字段:</strong>
                        <ul>
                            <li>id - 主键ID</li>
                            <li>data_source - 数据来源</li>
                            <li>error_type - 错误类型</li>
                            <li>error_description - 错误描述</li>
                            <li>original_value - 原始值</li>
                            <li>suggested_value - 建议值</li>
                            <li>status - 处理状态</li>
                            <li>created_date - 创建日期</li>
                            <li>processed_date - 处理日期</li>
                            <li>processor - 处理人</li>
                            <li>remark - 备注</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 使用说明</h2>
            <div class="test-result warning">
                <strong>操作步骤</strong>:
                <ol>
                    <li>在页面顶部选择要操作的表名（科目对照 或 异常数据）</li>
                    <li>点击"查询数据"按钮获取选定表的数据</li>
                    <li>可以通过Excel导入功能批量导入数据</li>
                    <li>在表格中直接编辑数据</li>
                    <li>点击"更新数据"按钮将修改保存到数据库</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>⚠️ 注意事项</h2>
            <div class="test-result warning">
                <ul>
                    <li>必须先选择表名才能进行查询和更新操作</li>
                    <li>更新操作会覆盖整个表的数据，请谨慎操作</li>
                    <li>Excel导入的数据格式需要与表结构匹配</li>
                    <li>建议在更新前先备份重要数据</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 测试建议</h2>
            <div class="test-result info">
                <strong>测试流程</strong>:
                <ol>
                    <li>启动后端服务: <code>cd backend && python -m uvicorn main:app --reload --port 8000</code></li>
                    <li>启动前端服务: <code>npm run dev</code></li>
                    <li>访问数据库管理页面</li>
                    <li>测试表名选择功能</li>
                    <li>测试查询不同表的数据</li>
                    <li>测试Excel导入功能</li>
                    <li>测试数据更新功能</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
