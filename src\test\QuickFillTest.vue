<template>
  <div class="quick-fill-test">
    <h2>速填模板功能测试</h2>
    
    <!-- 测试按钮 -->
    <div class="test-buttons">
      <button @click="testInvoiceEntry" class="test-btn">测试发票速录</button>
      <button @click="testMaterialSettlement" class="test-btn">测试物资结算</button>
      <button @click="testMaterialOutbound" class="test-btn">测试物资出库</button>
      <button @click="testDispatchSalary" class="test-btn">测试派遣工资</button>
    </div>
    
    <!-- 测试结果显示 -->
    <div class="test-results">
      <h3>测试结果:</h3>
      <div v-for="(result, index) in testResults" :key="index" class="test-result">
        <span :class="result.status">{{ result.message }}</span>
      </div>
    </div>
    
    <!-- 组件测试区域 -->
    <div class="component-test-area">
      <InvoiceEntryComponent v-if="showInvoiceEntry" @back="hideComponents" />
      <MaterialSettlementComponent v-if="showMaterialSettlement" @back="hideComponents" />
      <MaterialOutboundComponent v-if="showMaterialOutbound" @back="hideComponents" />
      <DispatchSalaryComponent v-if="showDispatchSalary" @back="hideComponents" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import InvoiceEntryComponent from '@/components/InvoiceEntryComponent.vue'
import MaterialSettlementComponent from '@/components/MaterialSettlementComponent.vue'
import MaterialOutboundComponent from '@/components/MaterialOutboundComponent.vue'
import DispatchSalaryComponent from '@/components/DispatchSalaryComponent.vue'

// 响应式数据
const testResults = ref([])
const showInvoiceEntry = ref(false)
const showMaterialSettlement = ref(false)
const showMaterialOutbound = ref(false)
const showDispatchSalary = ref(false)

// 添加测试结果
function addTestResult(message, status = 'success') {
  testResults.value.push({
    message,
    status,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 隐藏所有组件
function hideComponents() {
  showInvoiceEntry.value = false
  showMaterialSettlement.value = false
  showMaterialOutbound.value = false
  showDispatchSalary.value = false
  addTestResult('组件已隐藏')
}

// 测试发票速录
function testInvoiceEntry() {
  try {
    hideComponents()
    showInvoiceEntry.value = true
    addTestResult('发票速录组件加载成功')
  } catch (error) {
    addTestResult(`发票速录组件加载失败: ${error.message}`, 'error')
  }
}

// 测试物资结算
function testMaterialSettlement() {
  try {
    hideComponents()
    showMaterialSettlement.value = true
    addTestResult('物资结算组件加载成功')
  } catch (error) {
    addTestResult(`物资结算组件加载失败: ${error.message}`, 'error')
  }
}

// 测试物资出库
function testMaterialOutbound() {
  try {
    hideComponents()
    showMaterialOutbound.value = true
    addTestResult('物资出库组件加载成功')
  } catch (error) {
    addTestResult(`物资出库组件加载失败: ${error.message}`, 'error')
  }
}

// 测试派遣工资
function testDispatchSalary() {
  try {
    hideComponents()
    showDispatchSalary.value = true
    addTestResult('派遣工资组件加载成功')
  } catch (error) {
    addTestResult(`派遣工资组件加载失败: ${error.message}`, 'error')
  }
}
</script>

<style scoped>
.quick-fill-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.test-btn:hover {
  background: #337ecc;
}

.test-results {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  max-height: 200px;
  overflow-y: auto;
}

.test-result {
  margin-bottom: 8px;
  padding: 4px 8px;
  border-radius: 4px;
}

.test-result .success {
  color: #67c23a;
  background: #f0f9ff;
}

.test-result .error {
  color: #f56c6c;
  background: #fef0f0;
}

.component-test-area {
  border: 1px solid #ddd;
  border-radius: 8px;
  min-height: 400px;
  background: white;
}
</style>
