<template>
  <div class="sim-table-demo">
    <h1>SimTableComponent 演示</h1>
    
    <div class="demo-section">
      <h2>基础用法</h2>
      <p>美观的自定义表格组件，支持筛选和排序：</p>
      <SimTableComponent
        :data="basicData"
        :width="800"
        :height="300"
        :show-filter="true"
      />
    </div>

    <div class="demo-section">
      <h2>无筛选功能</h2>
      <SimTableComponent
        :data="simpleData"
        :width="600"
        :height="250"
        :show-filter="false"
      />
    </div>

    <div class="demo-section">
      <h2>分页表格</h2>
      <SimTableComponent
        :data="largeData"
        :width="900"
        :height="400"
        :show-filter="true"
        :show-pagination="true"
        :page-size="10"
      />
    </div>

    <div class="demo-section">
      <h2>动态数据</h2>
      <div class="controls">
        <el-button type="primary" @click="addRow">添加行</el-button>
        <el-button @click="removeRow">删除行</el-button>
        <el-button type="success" @click="randomizeData">随机数据</el-button>
      </div>
      <SimTableComponent
        ref="dynamicTableRef"
        :data="dynamicData"
        :width="700"
        :height="350"
        :show-filter="true"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SimTableComponent from '@/components/SimTableComponent.vue'

// 基础数据
const basicData = ref([
  ['姓名', '年龄', '城市', '薪资'],
  ['张三', 28, '北京', '¥12,000'],
  ['李四', 32, '上海', '¥15,000'],
  ['王五', 25, '广州', '¥10,000'],
  ['赵六', 30, '深圳', '¥13,500'],
  ['钱七', 27, '杭州', '¥11,800']
])

// 简单数据
const simpleData = ref([
  ['部门', '人数', '预算'],
  ['研发部', 20, '¥500,000'],
  ['销售部', 15, '¥300,000'],
  ['市场部', 8, '¥200,000'],
  ['行政部', 5, '¥100,000']
])

// 大数据集
const largeData = ref([
  ['编号', '产品名称', '价格', '库存', '分类', '状态'],
  ...Array.from({ length: 50 }, (_, i) => [
    `P${String(i + 1).padStart(3, '0')}`,
    `产品${i + 1}`,
    `¥${(Math.random() * 10000 + 1000).toFixed(0)}`,
    Math.floor(Math.random() * 100),
    ['电子产品', '服装', '食品', '图书', '家具'][Math.floor(Math.random() * 5)],
    ['在售', '缺货', '下架'][Math.floor(Math.random() * 3)]
  ])
])

// 动态数据
const dynamicData = ref([
  ['项目', '进度', '负责人', '截止日期'],
  ['项目A', '80%', '张三', '2024-12-31'],
  ['项目B', '60%', '李四', '2024-11-30'],
  ['项目C', '90%', '王五', '2024-10-15']
])

const dynamicTableRef = ref()

// 添加行
function addRow() {
  const newRow = [
    `项目${String.fromCharCode(65 + dynamicData.value.length - 1)}`,
    `${Math.floor(Math.random() * 100)}%`,
    ['张三', '李四', '王五', '赵六'][Math.floor(Math.random() * 4)],
    new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  ]
  dynamicData.value.push(newRow)
}

// 删除行
function removeRow() {
  if (dynamicData.value.length > 1) {
    dynamicData.value.pop()
  }
}

// 随机化数据
function randomizeData() {
  const projects = ['项目Alpha', '项目Beta', '项目Gamma', '项目Delta', '项目Epsilon']
  const people = ['张三', '李四', '王五', '赵六', '钱七', '孙八']
  
  dynamicData.value = [
    ['项目', '进度', '负责人', '截止日期'],
    ...Array.from({ length: Math.floor(Math.random() * 8) + 3 }, (_, i) => [
      projects[Math.floor(Math.random() * projects.length)],
      `${Math.floor(Math.random() * 100)}%`,
      people[Math.floor(Math.random() * people.length)],
      new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    ])
  ]
}
</script>

<style scoped>
.sim-table-demo {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 32px;
  font-size: 28px;
  font-weight: 600;
}

.demo-section {
  margin-bottom: 40px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.demo-section h2 {
  color: #34495e;
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.demo-section p {
  color: #7f8c8d;
  margin-bottom: 20px;
  font-size: 14px;
}

.controls {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .sim-table-demo {
    padding: 16px;
  }
  
  .demo-section {
    padding: 16px;
    margin-bottom: 24px;
  }
  
  .controls {
    flex-direction: column;
  }
}
</style>
