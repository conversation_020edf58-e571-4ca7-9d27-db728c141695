<template>
  <div class="scroll-test-container">
    <h1>VTable 滚动问题修复测试</h1>
    
    <div class="controls">
      <button @click="generateTestData" class="btn-primary">
        生成测试数据 ({{ testDataRows }}行)
      </button>
      <button @click="addMoreData" class="btn-secondary">
        添加更多数据 (+50行)
      </button>
      <button @click="clearData" class="btn-danger">
        清空数据
      </button>
      <button @click="fixScrollIssues" class="btn-warning">
        修复滚动问题
      </button>
      <button @click="scrollToBottom" class="btn-info">
        滚动到底部
      </button>
      <button @click="scrollToTop" class="btn-info">
        滚动到顶部
      </button>
    </div>

    <div class="info-panel">
      <div class="info-item">
        <label>数据行数:</label>
        <span>{{ tableData.length > 0 ? tableData.length - 1 : 0 }}</span>
      </div>
      <div class="info-item">
        <label>表格状态:</label>
        <span :class="tableStatus.class">{{ tableStatus.text }}</span>
      </div>
      <div class="info-item">
        <label>滚动位置:</label>
        <span>{{ scrollInfo }}</span>
      </div>
    </div>

    <div class="table-wrapper">
      <VTableComponent
        ref="tableRef"
        :data="tableData"
        :width="1200"
        :height="600"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="false"
        @data-change="onTableDataChange"
      />
    </div>

    <div class="debug-info">
      <h3>调试信息</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script>
import VTableComponent from '@/components/VTableComponent.vue';

export default {
  name: 'VTableScrollFixTest',
  components: {
    VTableComponent,
  },
  data() {
    return {
      tableData: [],
      testDataRows: 100,
      scrollInfo: '未知',
      debugInfo: {},
      tableStatus: {
        text: '未初始化',
        class: 'status-unknown'
      }
    };
  },
  mounted() {
    this.generateTestData();
    
    // 定期检查表格状态
    this.statusCheckInterval = setInterval(() => {
      this.checkTableStatus();
    }, 2000);
  },
  beforeUnmount() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }
  },
  methods: {
    generateTestData() {
      const headers = ['ID', '姓名', '年龄', '部门', '职位', '薪资', '入职日期', '状态', '备注'];
      const departments = ['技术部', '销售部', '市场部', '人事部', '财务部'];
      const positions = ['工程师', '经理', '主管', '专员', '总监'];
      const statuses = ['在职', '离职', '试用期', '实习'];
      
      const data = [headers];
      
      for (let i = 1; i <= this.testDataRows; i++) {
        const row = [
          i,
          `员工${i.toString().padStart(3, '0')}`,
          Math.floor(Math.random() * 40) + 22,
          departments[Math.floor(Math.random() * departments.length)],
          positions[Math.floor(Math.random() * positions.length)],
          Math.floor(Math.random() * 50000) + 5000,
          `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
          statuses[Math.floor(Math.random() * statuses.length)],
          `备注信息${i}`
        ];
        data.push(row);
      }
      
      this.tableData = data;
      console.log(`生成了 ${this.testDataRows} 行测试数据`);
    },

    addMoreData() {
      if (this.tableData.length === 0) {
        this.generateTestData();
        return;
      }

      const headers = this.tableData[0];
      const currentRows = this.tableData.length - 1;
      const departments = ['技术部', '销售部', '市场部', '人事部', '财务部'];
      const positions = ['工程师', '经理', '主管', '专员', '总监'];
      const statuses = ['在职', '离职', '试用期', '实习'];

      for (let i = 1; i <= 50; i++) {
        const rowId = currentRows + i;
        const row = [
          rowId,
          `员工${rowId.toString().padStart(3, '0')}`,
          Math.floor(Math.random() * 40) + 22,
          departments[Math.floor(Math.random() * departments.length)],
          positions[Math.floor(Math.random() * positions.length)],
          Math.floor(Math.random() * 50000) + 5000,
          `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
          statuses[Math.floor(Math.random() * statuses.length)],
          `备注信息${rowId}`
        ];
        this.tableData.push(row);
      }

      console.log(`添加了 50 行数据，总计 ${this.tableData.length - 1} 行`);
    },

    clearData() {
      this.tableData = [];
      this.scrollInfo = '无数据';
      this.debugInfo = {};
      this.tableStatus = {
        text: '已清空',
        class: 'status-empty'
      };
    },

    fixScrollIssues() {
      if (this.$refs.tableRef) {
        const result = this.$refs.tableRef.fixScrollIssues();
        if (result.success) {
          console.log('滚动问题修复成功:', result);
          this.tableStatus = {
            text: '修复成功',
            class: 'status-success'
          };
          alert(`滚动问题修复成功！\n可见行数: ${result.visibleRows}\n总行数: ${result.totalRows}`);
        } else {
          console.error('滚动问题修复失败');
          this.tableStatus = {
            text: '修复失败',
            class: 'status-error'
          };
          alert('滚动问题修复失败，请重试');
        }
      } else {
        alert('表格组件未找到');
      }
    },

    scrollToBottom() {
      if (this.$refs.tableRef) {
        const tableInstance = this.$refs.tableRef.getTableInstance();
        if (tableInstance) {
          // 滚动到最后一行
          const lastRowIndex = this.tableData.length - 2; // 减去表头
          if (lastRowIndex >= 0) {
            tableInstance.scrollToCell({ col: 0, row: lastRowIndex });
            console.log(`滚动到第 ${lastRowIndex + 1} 行`);
          }
        }
      }
    },

    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableInstance = this.$refs.tableRef.getTableInstance();
        if (tableInstance) {
          tableInstance.scrollToCell({ col: 0, row: 0 });
          console.log('滚动到顶部');
        }
      }
    },

    checkTableStatus() {
      if (this.$refs.tableRef) {
        const tableInstance = this.$refs.tableRef.getTableInstance();
        if (tableInstance) {
          try {
            const visibleRowCount = tableInstance.getVisibleRowCount?.() || 0;
            const totalRowCount = this.tableData.length - 1; // 减去表头
            const scrollTop = tableInstance.scrollTop || 0;
            const scrollLeft = tableInstance.scrollLeft || 0;

            this.scrollInfo = `Top: ${scrollTop}, Left: ${scrollLeft}`;
            this.debugInfo = {
              visibleRows: visibleRowCount,
              totalRows: totalRowCount,
              scrollTop: scrollTop,
              scrollLeft: scrollLeft,
              tableWidth: tableInstance.tableNoFrameWidth,
              tableHeight: tableInstance.tableNoFrameHeight,
              timestamp: new Date().toLocaleTimeString()
            };

            // 检查是否有数据行消失的问题
            if (totalRowCount > 0 && visibleRowCount === 0) {
              this.tableStatus = {
                text: '数据行消失！',
                class: 'status-error'
              };
            } else if (totalRowCount > 0) {
              this.tableStatus = {
                text: '正常',
                class: 'status-success'
              };
            } else {
              this.tableStatus = {
                text: '无数据',
                class: 'status-empty'
              };
            }
          } catch (error) {
            console.error('检查表格状态时出错:', error);
            this.tableStatus = {
              text: '检查出错',
              class: 'status-error'
            };
          }
        }
      }
    },

    onTableDataChange(newData) {
      this.tableData = newData;
      console.log('表格数据已更新:', newData.length - 1, '行');
    },
  },
};
</script>

<style scoped>
.scroll-test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary, .btn-danger, .btn-warning, .btn-info {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-info {
  background-color: #17a2b8;
  color: white;
}

.btn-info:hover {
  background-color: #138496;
}

.info-panel {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: bold;
  color: #495057;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

.status-empty {
  color: #6c757d;
  font-weight: bold;
}

.status-unknown {
  color: #ffc107;
  font-weight: bold;
}

.table-wrapper {
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.debug-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.debug-info h3 {
  margin-top: 0;
  color: #495057;
}

.debug-info pre {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 12px;
  overflow-x: auto;
}
</style>
