<template>
  <div class="enhanced-vtable-component">
    <!-- 查询面板 -->
    <div v-if="showQueryPanel" class="query-panel">
      <div class="query-header">
        <h3>数据查询</h3>
        <button @click="toggleQueryPanel" class="toggle-button">
          {{ queryPanelExpanded ? '收起' : '展开' }}
        </button>
      </div>
      
      <div v-show="queryPanelExpanded" class="query-content">
        <!-- 表选择 -->
        <div class="query-section">
          <label>选择数据表:</label>
          <select v-model="selectedTable" @change="onTableChange" class="table-select">
            <option value="">请选择数据表</option>
            <option
              v-for="table in availableTables"
              :key="table.value"
              :value="table.value"
            >
              {{ table.label }}
            </option>
          </select>
        </div>

        <!-- 查询条件 -->
        <div class="query-section">
          <div class="section-header">
            <label>查询条件:</label>
            <button @click="addQueryRule" class="add-rule-button">添加条件</button>
          </div>
          
          <div class="query-rules">
            <div
              v-for="(rule, index) in queryRules"
              :key="rule.id"
              class="query-rule"
            >
              <select v-model="rule.field" class="field-select">
                <option value="">选择字段</option>
                <option
                  v-for="field in getTableFields(selectedTable)"
                  :key="field.value"
                  :value="field.value"
                >
                  {{ field.label }}
                </option>
              </select>
              
              <select v-model="rule.operator" class="operator-select">
                <option value="equals">等于</option>
                <option value="contains">包含</option>
                <option value="starts_with">开头是</option>
                <option value="ends_with">结尾是</option>
                <option value="greater_than">大于</option>
                <option value="less_than">小于</option>
                <option value="between">介于</option>
                <option value="is_null">为空</option>
                <option value="is_not_null">不为空</option>
              </select>
              
              <input
                v-if="rule.operator !== 'is_null' && rule.operator !== 'is_not_null'"
                v-model="rule.value"
                placeholder="输入值"
                class="value-input"
              />
              
              <select v-if="index > 0" v-model="rule.logic" class="logic-select">
                <option value="and">AND</option>
                <option value="or">OR</option>
              </select>
              
              <button
                v-if="queryRules.length > 1"
                @click="removeQueryRule(index)"
                class="remove-rule-button"
              >
                删除
              </button>
            </div>
          </div>
        </div>

        <!-- 查询操作 -->
        <div class="query-actions">
          <button @click="executeQuery" class="execute-button" :disabled="!selectedTable || loading">
            {{ loading ? '查询中...' : '执行查询' }}
          </button>
          <button @click="clearResults" class="clear-button">清空结果</button>
          <button @click="loadTemplate" class="template-button">加载模板</button>
        </div>
      </div>
    </div>

    <!-- 筛选面板 -->
    <div v-if="showFilter && data.length > 0" class="filter-panel">
      <div class="filter-header">
        <h4>数据筛选</h4>
        <button @click="toggleFilterPanel" class="toggle-button">
          {{ filterPanelExpanded ? '收起' : '展开' }}
        </button>
      </div>
      
      <div v-show="filterPanelExpanded" class="filter-content">
        <div class="filter-conditions">
          <div v-for="(condition, index) in filterConditions" :key="index" class="filter-row">
            <select v-model="condition.column" class="column-select">
              <option value="">所有列</option>
              <option
                v-for="(col, colIndex) in columns"
                :key="colIndex"
                :value="colIndex.toString()"
              >
                {{ col.title }}
              </option>
            </select>
            
            <select v-model="condition.operator" class="operator-select">
              <option value="contains">包含</option>
              <option value="equals">等于</option>
              <option value="starts_with">开头是</option>
              <option value="ends_with">结尾是</option>
              <option value="greater_than">大于</option>
              <option value="less_than">小于</option>
            </select>
            
            <input
              v-model="condition.value"
              placeholder="输入关键词"
              @keyup.enter="applyFilter"
              class="value-input"
            />
            
            <button
              v-if="filterConditions.length > 1"
              @click="removeFilterCondition(index)"
              class="remove-condition-button"
            >
              删除
            </button>
          </div>
          
          <div class="filter-actions">
            <button @click="addFilterCondition" class="add-condition-button">添加条件</button>
            <select v-model="logicalOperator" class="logic-select">
              <option value="AND">AND</option>
              <option value="OR">OR</option>
            </select>
            <button @click="applyFilter" class="apply-filter-button">筛选</button>
            <button @click="resetFilter" class="reset-filter-button">重置</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <div ref="tableRef" class="vtable-container"></div>
    </div>

    <!-- 状态栏 -->
    <div v-if="data.length > 0" class="status-bar">
      <span class="record-count">
        共 {{ data.length > 0 ? data.length - 1 : 0 }} 条记录
      </span>
      <span v-if="filteredCount !== null" class="filtered-count">
        筛选后 {{ filteredCount }} 条记录
      </span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed, nextTick } from 'vue'
import * as VTable from '@visactor/vtable'

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 400
  },
  showFilter: {
    type: Boolean,
    default: true
  },
  showQueryPanel: {
    type: Boolean,
    default: false
  },
  availableTables: {
    type: Array,
    default: () => []
  },
  editable: {
    type: Boolean,
    default: false
  },
  enableCopyPaste: {
    type: Boolean,
    default: true
  },
  enableExcelImport: {
    type: Boolean,
    default: false
  },
  enablePushUpdate: {
    type: Boolean,
    default: false
  },
  tableOptions: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'data-change',
  'query-execute',
  'table-change',
  'cell-edit'
])

// 响应式数据
const tableRef = ref(null)
const vtableInstance = ref(null)
const loading = ref(false)

// 查询面板
const queryPanelExpanded = ref(true)
const selectedTable = ref('')
const queryRules = ref([])

// 筛选面板
const filterPanelExpanded = ref(false)
const filterConditions = ref([{ column: '', operator: 'contains', value: '' }])
const logicalOperator = ref('AND')
const filteredCount = ref(null)

// 表格数据
const columns = ref([])
const records = ref([])
const originalData = ref([])

// 计算属性
const hasData = computed(() => props.data.length > 1)

// 表格字段映射
const tableFieldsMap = {
  'subject_mapping': [
    { label: '总账科目长文本', value: 'account_text' },
    { label: '科目分类1', value: 'category1' },
    { label: '科目分类2', value: 'category2' },
    { label: '科目方向', value: 'direction' }
  ],
  'exception_data': [
    { label: '异常类型', value: 'exception_type' },
    { label: '异常描述', value: 'description' },
    { label: '发生时间', value: 'occurrence_time' }
  ],
  'integrated_contract': [
    { label: '合同编号', value: 'contract_code' },
    { label: '合同名称', value: 'contract_name' },
    { label: '合同金额', value: 'contract_amount' },
    { label: '客户名称', value: 'customer_name' }
  ],
  'master_data': [
    { label: '项目编号', value: 'project_code' },
    { label: '项目名称', value: 'project_name' },
    { label: '组织机构', value: 'organization_name' }
  ]
}

// 获取表格字段
const getTableFields = (tableName) => {
  return tableFieldsMap[tableName] || []
}

// 切换查询面板
const toggleQueryPanel = () => {
  queryPanelExpanded.value = !queryPanelExpanded.value
}

// 切换筛选面板
const toggleFilterPanel = () => {
  filterPanelExpanded.value = !filterPanelExpanded.value
}

// 表格变化处理
const onTableChange = () => {
  emit('table-change', selectedTable.value)
  // 清空查询规则
  queryRules.value = [createEmptyRule()]
}

// 创建空规则
const createEmptyRule = () => ({
  id: Date.now() + Math.random(),
  field: '',
  operator: 'equals',
  value: '',
  logic: 'and'
})

// 添加查询规则
const addQueryRule = () => {
  queryRules.value.push(createEmptyRule())
}

// 移除查询规则
const removeQueryRule = (index) => {
  if (queryRules.value.length > 1) {
    queryRules.value.splice(index, 1)
  }
}

// 执行查询
const executeQuery = () => {
  if (!selectedTable.value) {
    alert('请选择数据表')
    return
  }

  const validRules = queryRules.value.filter(rule => rule.field && rule.operator)
  
  const queryParams = {
    table: selectedTable.value,
    rules: validRules
  }

  emit('query-execute', queryParams)
}

// 清空结果
const clearResults = () => {
  emit('data-change', [])
}

// 加载模板
const loadTemplate = () => {
  // 根据选择的表加载预设查询模板
  if (selectedTable.value) {
    const templates = {
      'subject_mapping': [
        { field: 'account_text', operator: 'contains', value: '', logic: 'and' }
      ],
      'integrated_contract': [
        { field: 'contract_amount', operator: 'greater_than', value: '1000000', logic: 'and' }
      ]
    }
    
    const template = templates[selectedTable.value]
    if (template) {
      queryRules.value = template.map(rule => ({
        ...createEmptyRule(),
        ...rule
      }))
    }
  }
}

// 添加筛选条件
const addFilterCondition = () => {
  filterConditions.value.push({ column: '', operator: 'contains', value: '' })
}

// 移除筛选条件
const removeFilterCondition = (index) => {
  if (filterConditions.value.length > 1) {
    filterConditions.value.splice(index, 1)
  }
}

// 应用筛选
const applyFilter = () => {
  if (!vtableInstance.value || !originalData.value.length) return

  const validConditions = filterConditions.value.filter(condition => 
    condition.column !== '' && condition.value.trim() !== ''
  )

  if (validConditions.length === 0) {
    resetFilter()
    return
  }

  // 实现筛选逻辑
  const filteredRecords = originalData.value.filter((record, index) => {
    if (index === 0) return true // 保留表头

    const results = validConditions.map(condition => {
      const columnIndex = parseInt(condition.column)
      const cellValue = record[columnIndex]?.toString().toLowerCase() || ''
      const filterValue = condition.value.toLowerCase()

      switch (condition.operator) {
        case 'contains':
          return cellValue.includes(filterValue)
        case 'equals':
          return cellValue === filterValue
        case 'starts_with':
          return cellValue.startsWith(filterValue)
        case 'ends_with':
          return cellValue.endsWith(filterValue)
        case 'greater_than':
          return parseFloat(cellValue) > parseFloat(filterValue)
        case 'less_than':
          return parseFloat(cellValue) < parseFloat(filterValue)
        default:
          return true
      }
    })

    return logicalOperator.value === 'AND' 
      ? results.every(result => result)
      : results.some(result => result)
  })

  filteredCount.value = filteredRecords.length - 1
  updateTableData(filteredRecords)
}

// 重置筛选
const resetFilter = () => {
  filteredCount.value = null
  updateTableData(originalData.value)
}

// 更新表格数据
const updateTableData = (data) => {
  if (!vtableInstance.value || !data.length) return

  // 更新记录
  records.value = data.slice(1)
  
  // 重新渲染表格
  vtableInstance.value.updateOption({
    records: records.value
  })
}

// 初始化表格
const initTable = () => {
  if (!tableRef.value || !props.data.length) return

  // 准备数据
  originalData.value = [...props.data]
  
  if (props.data.length > 0) {
    // 生成列配置
    const headers = props.data[0]
    columns.value = headers.map((header, index) => ({
      field: index.toString(),
      title: header,
      width: 120,
      editor: props.editable ? 'input' : undefined
    }))

    // 准备记录数据
    records.value = props.data.slice(1).map(row => {
      const record = {}
      row.forEach((cell, index) => {
        record[index.toString()] = cell
      })
      return record
    })
  }

  // 创建表格实例
  const options = {
    container: tableRef.value,
    columns: columns.value,
    records: records.value,
    width: props.width,
    height: props.height,
    theme: VTable.themes.DEFAULT,
    ...props.tableOptions
  }

  vtableInstance.value = new VTable.ListTable(tableRef.value, options)

  // 添加事件监听
  if (props.editable) {
    vtableInstance.value.on('change_cell_value', (args) => {
      emit('cell-edit', args)
      
      // 更新原始数据
      const rowIndex = args.row + 1 // 加1因为原始数据包含表头
      const colIndex = parseInt(args.col)
      if (originalData.value[rowIndex]) {
        originalData.value[rowIndex][colIndex] = args.newValue
        emit('data-change', originalData.value)
      }
    })
  }
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData.length > 0) {
    originalData.value = [...newData]
    initTable()
  }
}, { deep: true })

// 组件挂载
onMounted(() => {
  queryRules.value = [createEmptyRule()]
  
  if (props.data.length > 0) {
    initTable()
  }
})

// 组件卸载
onBeforeUnmount(() => {
  if (vtableInstance.value) {
    vtableInstance.value.release()
    vtableInstance.value = null
  }
})
</script>

<style scoped>
.enhanced-vtable-component {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* 查询面板 */
.query-panel {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.query-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.query-header h3 {
  margin: 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.toggle-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.toggle-button:hover {
  background: #5a6268;
}

.query-content {
  padding: 16px;
}

.query-section {
  margin-bottom: 20px;
}

.query-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.add-rule-button {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.add-rule-button:hover {
  background: #218838;
}

.table-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.query-rules {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.query-rule {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.field-select,
.operator-select,
.logic-select {
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
}

.field-select {
  flex: 2;
}

.operator-select {
  flex: 1;
}

.logic-select {
  flex: 0.8;
}

.value-input {
  flex: 2;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
}

.remove-rule-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  flex-shrink: 0;
}

.remove-rule-button:hover {
  background: #c82333;
}

.query-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.execute-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.execute-button:hover:not(:disabled) {
  background: #0056b3;
}

.execute-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.clear-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.clear-button:hover {
  background: #5a6268;
}

.template-button {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.template-button:hover {
  background: #138496;
}

/* 筛选面板 */
.filter-panel {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.filter-header h4 {
  margin: 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.filter-content {
  padding: 16px;
}

.filter-conditions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.column-select {
  flex: 1.5;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
}

.remove-condition-button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  flex-shrink: 0;
}

.remove-condition-button:hover {
  background: #c82333;
}

.filter-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #dee2e6;
}

.add-condition-button {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.add-condition-button:hover {
  background: #218838;
}

.apply-filter-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.apply-filter-button:hover {
  background: #0056b3;
}

.reset-filter-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.reset-filter-button:hover {
  background: #5a6268;
}

/* 表格容器 */
.table-container {
  flex: 1;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.vtable-container {
  width: 100%;
  height: 100%;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  font-size: 12px;
  color: #6c757d;
}

.record-count {
  font-weight: 500;
}

.filtered-count {
  color: #007bff;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .query-rule,
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .query-actions,
  .filter-actions {
    flex-direction: column;
  }

  .field-select,
  .operator-select,
  .logic-select,
  .column-select {
    flex: none;
  }
}
</style>
