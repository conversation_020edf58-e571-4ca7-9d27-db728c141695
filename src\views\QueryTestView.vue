<template>
  <div class="test-container">
    <div class="test-header">
      <h2>综合查询系统测试</h2>
      <p>测试增强版综合查询功能</p>
    </div>

    <div class="test-sections">
      <!-- 原版综合查询 -->
      <div class="test-section">
        <h3>原版综合查询</h3>
        <router-link to="/universal-query" class="test-link">
          <div class="test-card">
            <div class="card-icon">🔍</div>
            <div class="card-content">
              <h4>原版综合查询</h4>
              <p>基于Univer的查询系统</p>
              <ul>
                <li>Univer电子表格展示</li>
                <li>规则配置面板</li>
                <li>模板查询</li>
                <li>Excel导入导出</li>
              </ul>
            </div>
          </div>
        </router-link>
      </div>

      <!-- 增强版综合查询 -->
      <div class="test-section">
        <h3>增强版综合查询</h3>
        <router-link to="/enhanced-universal-query" class="test-link">
          <div class="test-card enhanced">
            <div class="card-icon">🚀</div>
            <div class="card-content">
              <h4>增强版综合查询</h4>
              <p>基于VTable的增强查询系统</p>
              <ul>
                <li>✨ 集成查询面板</li>
                <li>💾 快照保存功能</li>
                <li>📋 版本控制</li>
                <li>🔄 数据筛选</li>
                <li>📊 VTable高性能展示</li>
                <li>📤 Excel导出</li>
              </ul>
            </div>
          </div>
        </router-link>
      </div>
    </div>

    <!-- 功能对比 -->
    <div class="comparison-section">
      <h3>功能对比</h3>
      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>功能</th>
              <th>原版</th>
              <th>增强版</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>数据展示</td>
              <td>Univer电子表格</td>
              <td>VTable高性能表格</td>
            </tr>
            <tr>
              <td>查询界面</td>
              <td>独立规则配置面板</td>
              <td>集成查询面板</td>
            </tr>
            <tr>
              <td>快照保存</td>
              <td>❌</td>
              <td>✅ 支持备注</td>
            </tr>
            <tr>
              <td>版本控制</td>
              <td>❌</td>
              <td>✅ 时间线展示</td>
            </tr>
            <tr>
              <td>数据筛选</td>
              <td>基础筛选</td>
              <td>高级多条件筛选</td>
            </tr>
            <tr>
              <td>数据编辑</td>
              <td>✅</td>
              <td>✅ 增强编辑</td>
            </tr>
            <tr>
              <td>Excel导入</td>
              <td>✅</td>
              <td>✅</td>
            </tr>
            <tr>
              <td>Excel导出</td>
              <td>✅</td>
              <td>✅ 增强导出</td>
            </tr>
            <tr>
              <td>性能</td>
              <td>中等</td>
              <td>高性能</td>
            </tr>
            <tr>
              <td>用户体验</td>
              <td>传统</td>
              <td>现代化</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 测试说明 -->
    <div class="test-instructions">
      <h3>测试说明</h3>
      <div class="instructions-content">
        <div class="instruction-item">
          <h4>1. 基础查询测试</h4>
          <p>选择数据表，配置查询条件，执行查询操作</p>
        </div>
        <div class="instruction-item">
          <h4>2. 快照功能测试</h4>
          <p>保存查询结果快照，添加备注信息，测试加载和删除功能</p>
        </div>
        <div class="instruction-item">
          <h4>3. 版本控制测试</h4>
          <p>创建多个快照版本，查看版本历史时间线</p>
        </div>
        <div class="instruction-item">
          <h4>4. 数据筛选测试</h4>
          <p>使用多条件筛选功能，测试AND/OR逻辑组合</p>
        </div>
        <div class="instruction-item">
          <h4>5. 导出功能测试</h4>
          <p>测试Excel导出功能，验证数据完整性</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 无需额外逻辑，纯展示页面
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;
}

.test-header h2 {
  color: #2c3e50;
  font-size: 32px;
  margin-bottom: 10px;
}

.test-header p {
  color: #7f8c8d;
  font-size: 18px;
}

.test-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
}

.test-section h3 {
  color: #34495e;
  margin-bottom: 20px;
  font-size: 24px;
}

.test-link {
  text-decoration: none;
  color: inherit;
}

.test-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.test-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.test-card.enhanced {
  border: 2px solid #3498db;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.card-icon {
  font-size: 48px;
  text-align: center;
  margin-bottom: 20px;
}

.card-content h4 {
  color: #2c3e50;
  font-size: 20px;
  margin-bottom: 10px;
}

.card-content p {
  color: #7f8c8d;
  margin-bottom: 15px;
  font-size: 14px;
}

.card-content ul {
  list-style: none;
  padding: 0;
}

.card-content li {
  color: #34495e;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
  font-size: 14px;
}

.card-content li::before {
  content: '•';
  color: #3498db;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.enhanced .card-content li::before {
  content: '✓';
  color: #27ae60;
}

.comparison-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.comparison-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 24px;
}

.comparison-table {
  overflow-x: auto;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.comparison-table th,
.comparison-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.comparison-table th {
  background: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

.comparison-table tr:hover {
  background: #f8f9fa;
}

.test-instructions {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-instructions h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 24px;
}

.instructions-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.instruction-item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.instruction-item h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 16px;
}

.instruction-item p {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .test-sections {
    grid-template-columns: 1fr;
  }
  
  .instructions-content {
    grid-template-columns: 1fr;
  }
  
  .test-container {
    padding: 15px;
  }
}
</style>
