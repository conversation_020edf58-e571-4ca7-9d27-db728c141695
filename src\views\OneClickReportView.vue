<template>
  <div class="report-dashboard">
    <!-- 左侧报告切换栏 -->
    <div class="report-sidebar">
      <button
        v-for="(report, index) in reports"
        :key="index"
        type="button"
        class="report-tab"
        :class="{ active: activeReport === report.id }"
        @click="activeReport = report.id"
      >
        <i :class="report.icon" class="tab-icon"></i>
        <span class="tab-title">{{ report.title }}</span>
      </button>
    </div>

    <!-- 右侧内容区 -->
    <div class="report-content">
      <div class="report-controls">
        <button @click="generateReport" class="generate-btn" :disabled="loading">
          <i class="el-icon-refresh"></i>
          {{ loading ? "生成中..." : "生成报告" }}
        </button>
      </div>
      <div v-if="loading" class="loading-spinner">正在生成报告...</div>
      <!-- 增加错误信息展示 -->
      <div v-else-if="errorMsg" class="placeholder error-message">{{ errorMsg }}</div>
      <!-- content-wrapper类名可能对样式有影响，予以保留 -->
      <div v-else-if="reportContent" class="content-wrapper">
        <!-- markdown-body是github-markdown-css的样式类 -->
        <div v-html="renderedMarkdown" class="markdown-body"></div>
      </div>
      <div v-else class="placeholder">
        请在左侧选择报告类型，然后点击“生成报告”按钮。
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import { marked } from "marked";
import "github-markdown-css/github-markdown.css";

export default {
  name: "OneClickReportView",
  data() {
    return {
      reports: [
        {
          id: "metrics",
          title: "指标情况简报",
          icon: "el-icon-data-analysis",
        },
        { id: "audit", title: "稽核情况简报", icon: "el-icon-document-checked" },
        { id: "fund", title: "资金情况简报", icon: "el-icon-money" },
      ],
      activeReport: "metrics", // 默认选中的报告
      reportContent: "", // 用于存储从后端获取的Markdown
      loading: false, // 控制加载状态
      errorMsg: "", // 用于存储错误信息
    };
  },
  computed: {
    renderedMarkdown() {
      return this.reportContent ? marked(this.reportContent) : "";
    },
  },
  watch: {
    activeReport(newReportId) {
      this.fetchReportData(newReportId);
    },
  },
  mounted() {
    // 组件加载后自动获取默认选中的报告
    this.fetchReportData(this.activeReport);
  },
  methods: {
    generateReport() {
      // "生成报告"按钮现在用于重新生成当前报告
      this.fetchReportData(this.activeReport);
    },
    async fetchReportData(reportId) {
      if (!reportId) return;
      this.loading = true;
      this.reportContent = ""; // 清空旧内容
      this.errorMsg = ""; // 清空错误信息
      try {
        // 假设您的API端点是 /api/report/:id
        // 您需要将其替换为真实的后端API地址
        const response = await axios.get(`http://localhost:8000/api/report/${reportId}`);
        this.reportContent = response.data; // 假设后端直接返回Markdown字符串
      } catch (error) {
        console.error(`获取报告 '${reportId}' 失败:`, error);
        // 显示错误信息
        this.errorMsg = "加载报告失败，请检查网络或联系管理员。";
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style scoped>
.report-dashboard {
  display: flex;
  /* height: calc(100vh - 84px); */
  height: 100%;
  background-color: #f5f7fa;
  overflow: hidden;
  padding: 20px 32px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
}

.report-sidebar {
  width: 200px;
  background-color: #fff;
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
  padding: 16px 0;
  overflow-y: auto;
}

.report-tab {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s;
  color: #606266;
  width: 100%;
  background: none;
  border: none;
  text-align: left;
  outline: none;
}

.report-tab:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.report-tab.active {
  background-color: #ecf5ff;
  color: #409eff;
  border-right: 3px solid #409eff;
}
.error-message {
  color: #f56c6c;
  border-color: #fde2e2;
  background-color: #fef0f0;
}
.github-markdown-body {
  padding: 10px;
  box-sizing: border-box;
  min-width: 200px;
  max-width: 980px;
  margin: 0 auto;
  padding: 45px;
}
.v-md-editor {
  border: 1px solid #ebeef5;
}
.tab-icon {
  margin-right: 8px;
  font-size: 18px;
}

.tab-title {
  font-size: 14px;
}

.report-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.report-controls {
  margin-bottom: 20px;
  text-align: right;
}

.generate-btn {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  background-color: #409eff;
  color: white;
  border: none;
  transition: background-color 0.3s;
}

.generate-btn:hover {
  background-color: #66b1ff;
}

.generate-btn:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  color: #909399;
  font-size: 16px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background-color: #f9fafc;
}

.content-wrapper {
  /* max-width: 900px; */
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 32px;
}
.report-section {
  width: 100%;
}

.report-section h2 {
  text-align: center;
  margin-bottom: 8px;
  color: #303133;
}

.report-date {
  text-align: center;
  color: #909399;
  margin-bottom: 24px;
  font-size: 14px;
}

.echart-container {
  margin: 24px 0;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
}

.markdown-content {
  margin-top: 32px;
}

.markdown-content h3 {
  margin: 24px 0 16px;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  font-size: 14px;
}

.report-table th,
.report-table td {
  border: 1px solid #ebeef5;
  padding: 12px 16px;
  text-align: left;
}

.report-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

.report-table tbody tr:hover {
  background-color: #f5f7fa;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-processing {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.status-pending {
  background-color: #fef0f0;
  color: #f56c6c;
}

.status-completed {
  background-color: #f0f9eb;
  color: #67c23a;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .report-dashboard {
    flex-direction: column;
    height: auto;
  }

  .report-sidebar {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 8px 0;
  }

  .report-tab {
    white-space: nowrap;
    padding: 8px 16px;
  }

  .report-content {
    padding: 16px;
  }

  .content-wrapper {
    padding: 16px;
  }
  .github-markdown-body {
    padding: 15px;
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: #606266;
}
</style>
