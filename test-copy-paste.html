<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTable 复制粘贴测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 4px;
            margin: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.danger {
            background: #dc3545;
        }
        .test-data {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .instructions {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .instructions h4 {
            margin-top: 0;
            color: #0c5460;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-left: 4px solid #007bff;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 VTable 复制粘贴功能测试工具</h1>
        
        <div class="instructions">
            <h4>📋 测试说明</h4>
            <p>这个工具帮助您测试VTable组件的复制粘贴功能是否正常工作。</p>
            <p><strong>重点测试场景：</strong>薪酬税务视图中的"算税底稿"表格，特别是 <span class="highlight">失业保险费、企业(职业)年金、其它扣款、调整收入、调整扣除</span> 字段的复制粘贴。</p>
        </div>

        <div class="test-section">
            <h3>🎯 测试步骤</h3>
            
            <div class="step">
                <strong>步骤 1：</strong> 准备测试数据
                <br>
                <button class="btn" onclick="copyTestValue('123')">复制单个数字: 123</button>
                <button class="btn" onclick="copyTestValue('测试文本')">复制单个文本: 测试文本</button>
                <button class="btn" onclick="copyTestValue('1000.50')">复制金额: 1000.50</button>
            </div>

            <div class="step">
                <strong>步骤 2：</strong> 准备多值数据
                <br>
                <button class="btn" onclick="copyMultipleValues()">复制多个值 (2行3列)</button>
                <button class="btn" onclick="copyTabSeparated()">复制制表符分隔值</button>
            </div>

            <div class="step">
                <strong>步骤 3：</strong> 打开测试页面
                <br>
                <a href="http://localhost:3001/salary-tax" target="_blank" class="btn success">打开薪酬税务视图</a>
                <a href="http://localhost:3001/vtable-copy-paste-test" target="_blank" class="btn">打开专用测试页面</a>
            </div>

            <div class="step">
                <strong>步骤 4：</strong> 执行测试
                <ol>
                    <li>在薪酬税务视图中，切换到"算税底稿"标签页</li>
                    <li>选中一个单元格（特别是失业保险费、企业(职业)年金等字段）</li>
                    <li>按 <code>Ctrl+V</code> 粘贴</li>
                    <li>检查值是否只出现在选中的单元格中，没有跨行</li>
                    <li>打开浏览器开发者工具查看控制台日志</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 预期结果</h3>
            <div class="result success">
                <strong>✅ 正确行为：</strong>
                <ul>
                    <li>单个值粘贴只影响选中的单元格</li>
                    <li>控制台显示详细的粘贴日志</li>
                    <li>多个VTable组件切换时不会相互干扰</li>
                    <li>粘贴操作快速响应，无延迟</li>
                </ul>
            </div>
            <div class="result error">
                <strong>❌ 错误行为：</strong>
                <ul>
                    <li>单个值粘贴影响多个单元格或跨行</li>
                    <li>粘贴操作无响应或报错</li>
                    <li>隐藏的VTable组件也响应粘贴事件</li>
                    <li>控制台出现错误信息</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🛠️ 调试工具</h3>
            <button class="btn warning" onclick="clearClipboard()">清空剪贴板</button>
            <button class="btn" onclick="checkClipboard()">检查剪贴板内容</button>
            <button class="btn danger" onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="test-section">
            <h3>📊 测试数据示例</h3>
            <div class="test-data">
                <strong>单个值：</strong><br>
                123<br>
                测试文本<br>
                1000.50<br><br>
                
                <strong>多个值（制表符分隔）：</strong><br>
                值1	值2	值3<br>
                值4	值5	值6<br><br>
                
                <strong>薪酬字段示例：</strong><br>
                失业保险费: 80<br>
                企业(职业)年金: 0<br>
                其它扣款: 100<br>
                调整收入: 500<br>
                调整扣除: 200
            </div>
        </div>
    </div>

    <script>
        // 复制单个测试值
        async function copyTestValue(value) {
            try {
                await navigator.clipboard.writeText(value);
                showResult(`✅ 已复制到剪贴板: "${value}"`, 'success');
            } catch (error) {
                showResult(`❌ 复制失败: ${error.message}`, 'error');
            }
        }

        // 复制多个值
        async function copyMultipleValues() {
            const multipleValues = '值1\t值2\t值3\n值4\t值5\t值6';
            try {
                await navigator.clipboard.writeText(multipleValues);
                showResult('✅ 已复制多个值到剪贴板 (2行3列)', 'success');
            } catch (error) {
                showResult(`❌ 复制失败: ${error.message}`, 'error');
            }
        }

        // 复制制表符分隔值
        async function copyTabSeparated() {
            const tabValues = '80\t0\t100\t500\t200';
            try {
                await navigator.clipboard.writeText(tabValues);
                showResult('✅ 已复制制表符分隔值到剪贴板', 'success');
            } catch (error) {
                showResult(`❌ 复制失败: ${error.message}`, 'error');
            }
        }

        // 清空剪贴板
        async function clearClipboard() {
            try {
                await navigator.clipboard.writeText('');
                showResult('✅ 剪贴板已清空', 'success');
            } catch (error) {
                showResult(`❌ 清空失败: ${error.message}`, 'error');
            }
        }

        // 检查剪贴板内容
        async function checkClipboard() {
            try {
                const text = await navigator.clipboard.readText();
                if (text) {
                    showResult(`📋 剪贴板内容: "${text}"`, 'success');
                } else {
                    showResult('📋 剪贴板为空', 'success');
                }
            } catch (error) {
                showResult(`❌ 读取失败: ${error.message}`, 'error');
            }
        }

        // 清空控制台
        function clearConsole() {
            console.clear();
            showResult('✅ 控制台已清空', 'success');
        }

        // 显示结果
        function showResult(message, type) {
            // 移除之前的结果
            const existingResult = document.querySelector('.dynamic-result');
            if (existingResult) {
                existingResult.remove();
            }

            // 创建新的结果元素
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type} dynamic-result`;
            resultDiv.textContent = message;
            
            // 插入到最后一个测试部分之后
            const lastSection = document.querySelector('.test-section:last-child');
            lastSection.parentNode.insertBefore(resultDiv, lastSection.nextSibling);

            // 3秒后自动移除
            setTimeout(() => {
                if (resultDiv.parentNode) {
                    resultDiv.remove();
                }
            }, 3000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 测试工具已准备就绪，请按照步骤进行测试', 'success');
        });
    </script>
</body>
</html>
