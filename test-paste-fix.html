<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTable 复制粘贴修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 8px;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #4caf50;
        }
        .bug-description {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin-bottom: 15px;
        }
        .fix-description {
            background: #d1ecf1;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #17a2b8;
            margin-bottom: 15px;
        }
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VTable 复制粘贴修复测试</h1>
        
        <div class="test-section">
            <div class="test-title">🐛 问题描述</div>
            <div class="bug-description">
                <strong>原始问题：</strong>复制粘贴有些bug，粘贴单个时好像会跨行
            </div>
            <div class="test-description">
                用户反馈在使用 VTableComponent 时，复制单个值进行粘贴操作时，数据可能会意外地跨行粘贴，而不是只粘贴到当前选中的单元格中。
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 修复方案</div>
            <div class="fix-description">
                <strong>修复内容：</strong>
                <ul>
                    <li>优化了 <code>keyboardOptions</code> 配置，添加了 <code>moveFocusCellOnEnter: false</code> 和 <code>editCellOnEnter: false</code></li>
                    <li>增强了粘贴事件监听，添加了数据类型检测和日志记录</li>
                    <li>重写了 <code>handlePaste</code> 方法，添加了单个值粘贴的特殊处理逻辑</li>
                    <li>添加了 <code>Ctrl+V</code> 键盘事件监听，使用自定义粘贴逻辑</li>
                    <li>通过剪贴板 API 检测粘贴内容类型，区分单个值和多值粘贴</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 测试用例 1：单个值粘贴</div>
            <div class="test-steps">
                <strong>测试步骤：</strong>
                <ol>
                    <li>在任意应用中复制一个单个文本值（如：<code>测试值</code>）</li>
                    <li>在 VTable 中选中一个单元格</li>
                    <li>使用 <code>Ctrl+V</code> 或点击"粘贴"按钮</li>
                    <li>观察粘贴结果</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>期望结果：</strong>值应该只粘贴到选中的单元格中，不应该跨行或跨列
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 测试用例 2：多值粘贴（表格数据）</div>
            <div class="test-steps">
                <strong>测试步骤：</strong>
                <ol>
                    <li>在 Excel 或其他表格应用中复制多行多列数据</li>
                    <li>在 VTable 中选中起始单元格</li>
                    <li>使用 <code>Ctrl+V</code> 或点击"粘贴"按钮</li>
                    <li>观察粘贴结果</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>期望结果：</strong>数据应该按照原始的行列结构正确粘贴
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 测试用例 3：单行多列粘贴</div>
            <div class="test-steps">
                <strong>测试步骤：</strong>
                <ol>
                    <li>复制一行包含制表符分隔的数据（如：<code>值1	值2	值3</code>）</li>
                    <li>在 VTable 中选中一个单元格</li>
                    <li>使用 <code>Ctrl+V</code> 或点击"粘贴"按钮</li>
                    <li>观察粘贴结果</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>期望结果：</strong>数据应该水平分布到多个列中
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 测试用例 4：多行单列粘贴</div>
            <div class="test-steps">
                <strong>测试步骤：</strong>
                <ol>
                    <li>复制多行数据（每行一个值，用换行符分隔）</li>
                    <li>在 VTable 中选中一个单元格</li>
                    <li>使用 <code>Ctrl+V</code> 或点击"粘贴"按钮</li>
                    <li>观察粘贴结果</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>期望结果：</strong>数据应该垂直分布到多个行中
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 调试信息</div>
            <div class="test-description">
                修复后的组件会在浏览器控制台输出详细的调试信息：
                <ul>
                    <li><code>粘贴数据:</code> - 显示粘贴事件的详细信息</li>
                    <li><code>粘贴的原始数据:</code> - 显示粘贴的原始数据内容</li>
                    <li><code>检测到单个值粘贴，防止跨行</code> - 当检测到单个值粘贴时的提示</li>
                    <li><code>剪贴板内容:</code> - 显示从剪贴板读取的内容</li>
                    <li><code>执行单个值粘贴到单元格:</code> - 显示单个值粘贴的目标位置</li>
                    <li><code>检测到 Ctrl+V 粘贴</code> - 当使用键盘快捷键粘贴时的提示</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📝 测试注意事项</div>
            <div class="test-description">
                <ul>
                    <li>测试时请打开浏览器开发者工具的控制台，观察调试输出</li>
                    <li>确保浏览器支持 Clipboard API（现代浏览器都支持）</li>
                    <li>如果自定义粘贴逻辑失败，组件会自动回退到默认的 VTable 粘贴行为</li>
                    <li>测试不同类型的数据：纯文本、数字、包含特殊字符的文本等</li>
                    <li>测试在不同选择状态下的粘贴行为：单个单元格选中、多个单元格选中、区域选中等</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 验收标准</div>
            <div class="expected-result">
                <strong>修复成功的标准：</strong>
                <ol>
                    <li>单个值粘贴时，数据只出现在选中的单元格中，不会跨行或跨列</li>
                    <li>多值粘贴时，数据按照原始结构正确分布</li>
                    <li>粘贴操作不会影响表格的其他功能（编辑、选择、滚动等）</li>
                    <li>控制台输出清晰的调试信息，便于问题排查</li>
                    <li>粘贴操作的性能良好，没有明显延迟</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
