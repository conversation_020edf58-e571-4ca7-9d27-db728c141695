/**
 * VTable 增强功能测试脚本
 * 用于验证新增的删除行、添加行、Excel导入导出、撤销等功能
 */

// 模拟测试数据
const testData = [
  [
    '工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止',
    '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息',
    '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'
  ],
  ['E001', '张三', '身份证', '110101199001011234', '2025-01-01', '2025-01-31', 15000, 1000, 0, 1000, 0, 2000, 0, 0],
  ['E002', '李四', '身份证', '110101199102022345', '2025-01-01', '2025-01-31', 12000, 1000, 400, 0, 1500, 2000, 0, 0],
  ['E003', '王五', '身份证', '110101199203033456', '2025-01-01', '2025-01-31', 18000, 2000, 0, 1000, 0, 2000, 1000, 0]
]

// 测试用例
const testCases = {
  // 测试数据验证
  testDataValidation() {
    console.log('🧪 测试数据验证功能...')
    
    // 正确格式的数据
    const validData = [
      ['工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止', '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'],
      ['E004', '赵六', '身份证', '110101199304044567', '2025-01-01', '2025-01-31', 20000, 1000, 800, 1000, 0, 2000, 0, 500]
    ]
    
    // 错误格式的数据（列数不匹配）
    const invalidData = [
      ['工号', '姓名', '证件类型'], // 列数不足
      ['E005', '钱七', '身份证']
    ]
    
    console.log('✅ 有效数据格式:', validateDataFormat(validData))
    console.log('❌ 无效数据格式:', validateDataFormat(invalidData))
  },

  // 测试撤销栈功能
  testUndoStack() {
    console.log('🧪 测试撤销栈功能...')
    
    const undoStack = []
    const maxSteps = 3 // 简化测试，只保存3步
    
    // 模拟保存状态
    function saveState(data, operation) {
      const state = {
        timestamp: new Date().toISOString(),
        operation: operation,
        data: JSON.parse(JSON.stringify(data))
      }
      
      undoStack.push(state)
      
      if (undoStack.length > maxSteps) {
        undoStack.shift()
      }
      
      console.log(`💾 保存状态: ${operation}, 栈大小: ${undoStack.length}`)
    }
    
    // 模拟操作序列
    let currentData = [...testData]
    
    saveState(currentData, '初始状态')
    
    // 添加行
    currentData.push(['E004', '赵六', '身份证', '110101199304044567', '2025-01-01', '2025-01-31', 20000, 1000, 800, 1000, 0, 2000, 0, 500])
    saveState(currentData, '添加行')
    
    // 删除行
    currentData.splice(2, 1)
    saveState(currentData, '删除行')
    
    // 再添加行
    currentData.push(['E005', '钱七', '身份证', '110101199405055678', '2025-01-01', '2025-01-31', 22000, 1500, 0, 1200, 0, 2000, 500, 0])
    saveState(currentData, '再次添加行')
    
    // 测试撤销
    if (undoStack.length > 0) {
      const lastState = undoStack.pop()
      console.log(`↩️ 撤销操作: ${lastState.operation}`)
      console.log(`📊 恢复数据行数: ${lastState.data.length}`)
    }
    
    console.log(`✅ 撤销栈测试完成，剩余步数: ${undoStack.length}`)
  },

  // 测试Excel数据处理
  testExcelDataProcessing() {
    console.log('🧪 测试Excel数据处理功能...')
    
    // 模拟从Excel读取的原始数据
    const rawExcelData = [
      // 标题行
      ['工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止', '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'],
      // 数据行
      ['E006', '孙八', '身份证', '110101199506066789', '2025-01-01', '2025-01-31', 25000, 2000, 1000, 1500, 0, 2000, 0, 1000],
      ['E007', '周九', '身份证', '110101199607077890', '2025-01-01', '2025-01-31', 28000, 1000, 0, 1000, 2000, 2000, 1000, 0],
      // 空行（应该被过滤）
      ['', '', '', '', '', '', '', '', '', '', '', '', '', ''],
      // 部分空值的行
      ['E008', '吴十', '身份证', '110101199708088901', '2025-01-01', '2025-01-31', 30000, '', 500, '', 1800, 2000, '', 800]
    ]
    
    // 处理Excel数据
    const processedData = processExcelData(rawExcelData)
    console.log('📊 处理后的数据行数:', processedData.length)
    console.log('📋 处理后的数据:', processedData)
    
    // 验证数据完整性
    const hasEmptyRows = processedData.some(row => row.every(cell => cell === ''))
    console.log('🔍 是否包含空行:', hasEmptyRows ? '是' : '否')
    
    console.log('✅ Excel数据处理测试完成')
  },

  // 测试行操作功能
  testRowOperations() {
    console.log('🧪 测试行操作功能...')
    
    let tableData = [...testData]
    console.log('📊 初始数据行数:', tableData.length - 1) // 减去标题行
    
    // 测试添加行
    const newRow = ['E004', '赵六', '身份证', '110101199304044567', '2025-01-01', '2025-01-31', 20000, 1000, 800, 1000, 0, 2000, 0, 500]
    tableData.push(newRow)
    console.log('➕ 添加行后数据行数:', tableData.length - 1)
    
    // 测试删除行（删除索引为2的行）
    const deletedRow = tableData.splice(2, 1)
    console.log('🗑️ 删除的行:', deletedRow[0])
    console.log('🗑️ 删除行后数据行数:', tableData.length - 1)
    
    // 测试批量添加
    const batchRows = [
      ['E005', '钱七', '身份证', '110101199405055678', '2025-01-01', '2025-01-31', 22000, 1500, 0, 1200, 0, 2000, 500, 0],
      ['E006', '孙八', '身份证', '110101199506066789', '2025-01-01', '2025-01-31', 25000, 2000, 1000, 1500, 0, 2000, 0, 1000]
    ]
    tableData.push(...batchRows)
    console.log('📦 批量添加后数据行数:', tableData.length - 1)
    
    console.log('✅ 行操作测试完成')
  },

  // 运行所有测试
  runAllTests() {
    console.log('🚀 开始运行VTable增强功能测试...\n')
    
    this.testDataValidation()
    console.log('')
    
    this.testUndoStack()
    console.log('')
    
    this.testExcelDataProcessing()
    console.log('')
    
    this.testRowOperations()
    console.log('')
    
    console.log('🎉 所有测试完成！')
  }
}

// 辅助函数

// 验证数据格式
function validateDataFormat(data) {
  if (!data || data.length === 0) return false
  
  const expectedColumnCount = 14 // 专项附加扣除表的列数
  
  // 检查每行的列数
  for (let i = 0; i < data.length; i++) {
    if (data[i].length !== expectedColumnCount) {
      return false
    }
  }
  
  return true
}

// 处理Excel数据
function processExcelData(rawData) {
  if (!rawData || rawData.length === 0) return []
  
  // 过滤空行
  const filteredData = rawData.filter(row => {
    return row.some(cell => cell !== '' && cell != null)
  })
  
  // 处理单元格值
  return filteredData.map(row => {
    return row.map(cell => {
      if (cell == null || cell === undefined) return ''
      if (typeof cell === 'object') {
        if (cell.formula) return cell.result || ''
        if (cell.text) return cell.text
        return String(cell)
      }
      return String(cell)
    })
  })
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  // 将测试函数挂载到全局对象，方便在控制台调用
  window.vtableTests = testCases
  
  // 自动运行测试
  console.log('VTable增强功能测试脚本已加载')
  console.log('在控制台输入 vtableTests.runAllTests() 运行所有测试')
  console.log('或者运行单个测试，如: vtableTests.testDataValidation()')
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testCases
}

// 导出测试用例
export default testCases
