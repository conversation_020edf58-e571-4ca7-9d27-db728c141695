from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import duckdb
import pandas as pd
from datetime import datetime
import json
import os

from . import ollama_api

app = FastAPI(title="财务台账查询系统", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(ollama_api.router, prefix="/ollama", tags=["Ollama AI Chat"])

# 数据库连接
DB_PATH = "financial_data.duckdb"

# 数据库管理请求模型
class DbAdminQueryRequest(BaseModel):
    table_name: str

# 综合查询请求模型
class QueryRule(BaseModel):
    id: int
    table: str
    field: str
    operator: str
    value: str
    logic: str

class UniversalQueryRequest(BaseModel):
    rules: List[QueryRule]

class DbAdminUpdateRequest(BaseModel):
    table_name: str
    data: List[List[Any]]

# 请求模型
class QueryRequest(BaseModel):
    filters: Dict[str, Any]
    timestamp: str

# 响应模型
class QueryResponse(BaseModel):
    code: int
    message: str
    data: List[List[Any]]
    timestamp: str

# 初始化数据库和表结构
def init_database():
    """初始化数据库和表结构"""
    conn = duckdb.connect(DB_PATH)
    
    # 创建一体化合同台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS integrated_contract (
            search_key VARCHAR,
            organization_name VARCHAR,
            project_name VARCHAR,
            project_code VARCHAR,
            contract_name VARCHAR,
            contract_code VARCHAR,
            original_contract_code VARCHAR,
            contract_business_content VARCHAR,
            customer_name VARCHAR,
            customer_code VARCHAR,
            contract_type VARCHAR,
            contract_amount DOUBLE,
            tax_rate VARCHAR,
            settlement_amount DOUBLE,
            prepaid_amount DOUBLE,
            paid_amount DOUBLE,
            invoice_amount DOUBLE,
            payment_ratio DOUBLE,
            payable_balance DOUBLE,
            overdue_amount DOUBLE
        )
    """)
    
    # 创建专项储备表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS special_reserve (
            voucher_number VARCHAR,
            fiscal_year VARCHAR,
            profit_center VARCHAR,
            profit_center_desc VARCHAR,
            text VARCHAR,
            posting_date DATE,
            input_date DATE,
            safety_production_fee DOUBLE,
            type VARCHAR
        )
    """)
    
    # 创建主数据表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS master_data (
            project_code VARCHAR,
            profit_center VARCHAR,
            accounting_organization VARCHAR,
            profit_center_desc VARCHAR,
            profit_center_group_desc VARCHAR
        )
    """)
    
    # 创建付款台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS payment_ledger (
            fiscal_year VARCHAR,
            posting_date DATE,
            input_date DATE,
            supplier_type VARCHAR,
            voucher_number VARCHAR,
            profit_center VARCHAR,
            profit_center_desc VARCHAR,
            supplier VARCHAR,
            supplier_desc VARCHAR,
            contract VARCHAR,
            contract_desc VARCHAR,
            text VARCHAR,
            platform_document_number VARCHAR,
            total_payment_amount DOUBLE,
            performance_bond_deduction DOUBLE,
            supply_chain_factoring DOUBLE,
            cost_offset DOUBLE,
            this_profit_center DOUBLE,
            internal_bank_or_deposit DOUBLE,
            internal_bank_customer VARCHAR
        )
    """)
    
    # 创建保证金台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS guarantee_ledger (
            serial_number DOUBLE,
            organization VARCHAR,
            organization_name VARCHAR,
            project_code VARCHAR,
            project_name VARCHAR,
            currency VARCHAR,
            deadline TIMESTAMP,
            guarantee_type VARCHAR,
            receiving_party VARCHAR,
            receiving_party_group VARCHAR,
            actual_paid_amount DOUBLE,
            actual_recovered_amount DOUBLE,
            remaining_amount DOUBLE,
            responsible_person VARCHAR
        )
    """)

    # 创建科目对照表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS subject_mapping (
            id INTEGER PRIMARY KEY,
            subject_code VARCHAR,
            subject_name VARCHAR,
            mapping_code VARCHAR,
            mapping_name VARCHAR,
            category VARCHAR,
            status VARCHAR,
            created_date DATE,
            updated_date DATE,
            remark VARCHAR
        )
    """)

    # 创建异常数据表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS exception_data (
            id INTEGER PRIMARY KEY,
            data_source VARCHAR,
            error_type VARCHAR,
            error_description VARCHAR,
            original_value VARCHAR,
            suggested_value VARCHAR,
            status VARCHAR,
            created_date DATE,
            processed_date DATE,
            processor VARCHAR,
            remark VARCHAR
        )
    """)

    conn.close()

# 构建查询条件
def build_where_clause(filters: Dict[str, Any], table_name: str) -> str:
    """构建WHERE子句"""
    conditions = []
    
    for key, value in filters.items():
        if not value:
            continue
            
        # 处理金额范围查询
        if key.endswith('Min') and value:
            field_name = key[:-3]  # 移除'Min'后缀
            conditions.append(f"{field_name} >= {float(value)}")
        elif key.endswith('Max') and value:
            field_name = key[:-3]  # 移除'Max'后缀
            conditions.append(f"{field_name} <= {float(value)}")
        # 处理日期范围查询
        elif isinstance(value, list) and len(value) == 2:
            if value[0] and value[1]:
                conditions.append(f"{key} BETWEEN '{value[0]}' AND '{value[1]}'")
        # 处理文本查询
        elif isinstance(value, str) and value.strip():
            conditions.append(f"{key} LIKE '%{value.strip()}%'")
        # 处理数字查询
        elif isinstance(value, (int, float)):
            conditions.append(f"{key} = {value}")
    
    return " AND ".join(conditions) if conditions else "1=1"

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库"""
    init_database()

@app.get("/")
async def root():
    return {"message": "财务台账查询系统API"}

@app.post("/api/query/integrated-contract", response_model=QueryResponse)
async def query_integrated_contract(request: QueryRequest):
    """查询一体化合同台账"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "integrated_contract")
        query = f"""
            SELECT * FROM integrated_contract 
            WHERE {where_clause}
            ORDER BY contract_code DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        # 构造返回数据，第一行为列名
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/special-reserve", response_model=QueryResponse)
async def query_special_reserve(request: QueryRequest):
    """查询专项储备"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "special_reserve")
        query = f"""
            SELECT * FROM special_reserve 
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/master-data", response_model=QueryResponse)
async def query_master_data(request: QueryRequest):
    """查询主数据"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "master_data")
        query = f"""
            SELECT * FROM master_data
            WHERE {where_clause}
            ORDER BY project_code
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/payment-ledger", response_model=QueryResponse)
async def query_payment_ledger(request: QueryRequest):
    """查询付款台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "payment_ledger")
        query = f"""
            SELECT * FROM payment_ledger
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/guarantee-ledger", response_model=QueryResponse)
async def query_guarantee_ledger(request: QueryRequest):
    """查询保证金台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "guarantee_ledger")
        query = f"""
            SELECT * FROM guarantee_ledger
            WHERE {where_clause}
            ORDER BY deadline DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 导入额外的API端点
from additional_endpoints import (
    query_internal_bank,
    query_internal_reconciliation,
    query_subcontractor_settlement,
    query_external_confirmation,
    query_payable_by_supplier,
    query_cost_ledger,
    query_receipt_ledger,
    query_fund_management
)

# 导入资金流动API
from capital_flow_api import router as capital_flow_router

# 导入薪酬个税API
from salary_tax_api import router as salary_tax_router

# 注册额外的API端点
@app.post("/api/query/internal-bank", response_model=QueryResponse)
async def api_query_internal_bank(request: QueryRequest):
    return await query_internal_bank(request)

@app.post("/api/query/internal-reconciliation", response_model=QueryResponse)
async def api_query_internal_reconciliation(request: QueryRequest):
    return await query_internal_reconciliation(request)

@app.post("/api/query/subcontractor-settlement", response_model=QueryResponse)
async def api_query_subcontractor_settlement(request: QueryRequest):
    return await query_subcontractor_settlement(request)

@app.post("/api/query/external-confirmation", response_model=QueryResponse)
async def api_query_external_confirmation(request: QueryRequest):
    return await query_external_confirmation(request)

@app.post("/api/query/payable-by-supplier", response_model=QueryResponse)
async def api_query_payable_by_supplier(request: QueryRequest):
    return await query_payable_by_supplier(request)

@app.post("/api/query/cost-ledger", response_model=QueryResponse)
async def api_query_cost_ledger(request: QueryRequest):
    return await query_cost_ledger(request)

@app.post("/api/query/receipt-ledger", response_model=QueryResponse)
async def api_query_receipt_ledger(request: QueryRequest):
    return await query_receipt_ledger(request)

@app.post("/api/query/fund-management", response_model=QueryResponse)
async def api_query_fund_management(request: QueryRequest):
    return await query_fund_management(request)

# 注册资金流动API路由
app.include_router(capital_flow_router)

# 注册薪酬个税API路由
app.include_router(salary_tax_router)

# 数据库管理API接口
@app.get("/api/db-admin/query")
async def db_admin_query(table_name: str):
    """数据库管理查询接口"""
    try:
        # 验证表名
        allowed_tables = ["subject_mapping", "exception_data"]
        if table_name not in allowed_tables:
            raise HTTPException(status_code=400, detail=f"不支持的表名: {table_name}")

        conn = duckdb.connect(DB_PATH)

        # 查询数据
        query = f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 1000"
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        # 构造返回数据，第一行为列名
        data = [columns] + [list(row) for row in result]

        conn.close()

        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/db-admin/update")
async def db_admin_update(request: DbAdminUpdateRequest):
    """数据库管理更新接口"""
    try:
        # 验证表名
        allowed_tables = ["subject_mapping", "exception_data"]
        if request.table_name not in allowed_tables:
            raise HTTPException(status_code=400, detail=f"不支持的表名: {request.table_name}")

        if not request.data or len(request.data) < 2:
            raise HTTPException(status_code=400, detail="数据格式错误，至少需要表头和一行数据")

        conn = duckdb.connect(DB_PATH)

        # 获取表头和数据
        headers = request.data[0]
        data_rows = request.data[1:]

        # 清空表数据
        conn.execute(f"DELETE FROM {request.table_name}")

        # 构建插入语句
        placeholders = ", ".join(["?" for _ in headers])
        insert_query = f"INSERT INTO {request.table_name} ({', '.join(headers)}) VALUES ({placeholders})"

        # 批量插入数据
        for row in data_rows:
            # 确保行数据长度与表头一致
            if len(row) != len(headers):
                # 补齐或截断数据
                if len(row) < len(headers):
                    row.extend([None] * (len(headers) - len(row)))
                else:
                    row = row[:len(headers)]
            conn.execute(insert_query, row)

        conn.close()

        return {"message": f"成功更新 {len(data_rows)} 条记录到表 {request.table_name}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

# 综合查询API接口
@app.post("/api/universal-query")
async def universal_query(request: UniversalQueryRequest):
    """综合查询接口 - 支持动态查询规则"""
    try:
        if not request.rules:
            raise HTTPException(status_code=400, detail="查询规则不能为空")

        conn = duckdb.connect(DB_PATH)

        # 构建查询语句
        query_parts = []
        params = []

        # 表名映射 - 确保表存在
        table_mapping = {
            'subject_mapping': 'subject_mapping',
            'exception_data': 'exception_data',
            'integrated_contract': 'integrated_contract',
            'master_data': 'master_data',
            'payment_ledger': 'payment_ledger',
            'guarantee_ledger': 'guarantee_ledger',
            'internal_bank': 'internal_bank',
            'internal_reconciliation': 'internal_reconciliation'
        }

        # 字段映射 - 确保字段存在
        field_mapping = {
            'subject_mapping': {
                'gl_account_text': 'gl_account_text',
                'category1': 'category1',
                'category2': 'category2',
                'direction': 'direction'
            },
            'exception_data': {
                'exception_type': 'exception_type',
                'description': 'description',
                'occur_date': 'occur_date',
                'status': 'status'
            },
            'integrated_contract': {
                'contract_number': 'contract_number',
                'contract_name': 'contract_name',
                'customer_name': 'customer_name',
                'contract_amount': 'contract_amount',
                'contract_type': 'contract_type'
            },
            'master_data': {
                'project_code': 'project_code',
                'profit_center': 'profit_center',
                'accounting_org': 'accounting_org',
                'profit_center_desc': 'profit_center_desc'
            }
        }

        # 获取所有涉及的表
        tables_used = set()
        for rule in request.rules:
            if rule.table in table_mapping:
                tables_used.add(rule.table)

        if not tables_used:
            raise HTTPException(status_code=400, detail="没有找到有效的查询表")

        # 如果只有一个表，直接查询
        if len(tables_used) == 1:
            table_name = list(tables_used)[0]
            actual_table = table_mapping[table_name]

            # 构建WHERE条件
            where_conditions = []
            for i, rule in enumerate(request.rules):
                if rule.table != table_name:
                    continue

                field_map = field_mapping.get(table_name, {})
                actual_field = field_map.get(rule.field, rule.field)

                condition = build_condition(actual_field, rule.operator, rule.value, params)
                if condition:
                    if i > 0 and where_conditions:
                        where_conditions.append(f" {rule.logic.upper()} ")
                    where_conditions.append(condition)

            where_clause = "".join(where_conditions) if where_conditions else "1=1"
            query = f"SELECT * FROM {actual_table} WHERE {where_clause} LIMIT 1000"

        else:
            # 多表查询 - 简化处理，返回模拟数据
            return {
                "success": True,
                "data": generate_mock_query_result(),
                "message": "多表查询功能开发中，返回模拟数据"
            }

        # 执行查询
        result = conn.execute(query, params).fetchall()
        columns = [desc[0] for desc in conn.description]

        # 构造返回数据
        data = [columns] + [list(row) for row in result]

        conn.close()

        return {
            "success": True,
            "data": data,
            "message": f"查询成功，共找到 {len(result)} 条记录"
        }

    except Exception as e:
        print(f"综合查询失败: {str(e)}")
        # 返回模拟数据作为fallback
        return {
            "success": True,
            "data": generate_mock_query_result(),
            "message": f"查询失败，返回模拟数据: {str(e)}"
        }

def build_condition(field: str, operator: str, value: str, params: List):
    """构建查询条件"""
    if operator == "eq":
        params.append(value)
        return f"{field} = ?"
    elif operator == "like":
        params.append(f"%{value}%")
        return f"{field} LIKE ?"
    elif operator == "gt":
        params.append(value)
        return f"{field} > ?"
    elif operator == "lt":
        params.append(value)
        return f"{field} < ?"
    elif operator == "gte":
        params.append(value)
        return f"{field} >= ?"
    elif operator == "lte":
        params.append(value)
        return f"{field} <= ?"
    elif operator == "ne":
        params.append(value)
        return f"{field} != ?"
    elif operator == "null":
        return f"{field} IS NULL"
    elif operator == "not_null":
        return f"{field} IS NOT NULL"
    else:
        return None

def generate_mock_query_result():
    """生成模拟查询结果"""
    headers = ['ID', '项目名称', '合同编号', '金额', '状态', '创建时间', '备注']
    mock_data = []

    for i in range(1, 21):
        mock_data.append([
            i,
            f"项目{i:03d}",
            f"HT{i:04d}",
            f"{(i * 50000 + 100000):.2f}",
            "进行中" if i % 3 == 0 else "已完成",
            f"2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
            f"这是第{i}个项目的备注信息"
        ])

    return [headers] + mock_data

# Snapshot API 接口
@app.post("/api/save-snapsheet")
async def save_snapsheet(request: List[Any]):
    """保存快照"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建快照表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS query_snapshots (
                id INTEGER PRIMARY KEY,
                snapshot_name VARCHAR,
                snapshot_data TEXT,
                remark VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        snapshot_data = request[0]
        remark = request[1] if len(request) > 1 else ""

        # 生成快照名称
        from datetime import datetime
        snapshot_name = f"查询快照_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 保存快照
        conn.execute("""
            INSERT INTO query_snapshots (snapshot_name, snapshot_data, remark)
            VALUES (?, ?, ?)
        """, [snapshot_name, json.dumps(snapshot_data, ensure_ascii=False), remark])

        conn.close()

        return {"success": True, "message": "快照保存成功", "snapshot_name": snapshot_name}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存快照失败: {str(e)}")

@app.post("/api/get-snapsheet")
async def get_snapsheet(request: Dict[str, Any]):
    """获取快照"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建快照表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS query_snapshots (
                id INTEGER PRIMARY KEY,
                snapshot_name VARCHAR,
                snapshot_data TEXT,
                remark VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        period_id = request.get('periodId', 'max')

        if period_id == 'max':
            # 获取最新快照
            result = conn.execute("""
                SELECT snapshot_data FROM query_snapshots
                ORDER BY created_at DESC LIMIT 1
            """).fetchone()
        else:
            # 获取指定ID的快照
            result = conn.execute("""
                SELECT snapshot_data FROM query_snapshots
                WHERE id = ?
            """, [period_id]).fetchone()

        conn.close()

        if result:
            return json.loads(result[0])
        else:
            # 返回默认快照结构
            return {
                "id": "default-query-workbook",
                "name": "综合查询结果",
                "sheets": {
                    "查询结果": {
                        "id": "sheet1",
                        "name": "查询结果",
                        "cellData": {}
                    }
                }
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取快照失败: {str(e)}")

@app.get("/api/snapshots/list")
async def list_snapshots():
    """获取快照列表"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建快照表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS query_snapshots (
                id INTEGER PRIMARY KEY,
                snapshot_name VARCHAR,
                snapshot_data TEXT,
                remark VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        result = conn.execute("""
            SELECT id, snapshot_name, remark, created_at
            FROM query_snapshots
            ORDER BY created_at DESC
        """).fetchall()

        conn.close()

        snapshots = []
        for row in result:
            snapshots.append({
                "id": row[0],
                "name": row[1],
                "remark": row[2] or "",
                "created_at": row[3]
            })

        return snapshots
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取快照列表失败: {str(e)}")

@app.post("/api/delete-snapsheet")
async def delete_snapsheet(snapshot_id: int):
    """删除快照"""
    try:
        conn = duckdb.connect(DB_PATH)

        conn.execute("DELETE FROM query_snapshots WHERE id = ?", [snapshot_id])

        conn.close()

        return {"success": True, "message": "快照删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除快照失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
