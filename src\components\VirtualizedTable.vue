<template>
  <div class="virtualized-table-container">
    <div class="table-header">
      <div
        v-for="column in columns"
        :key="column.key"
        class="header-cell"
        :style="{ width: column.width ? column.width + 'px' : 'auto' }"
      >
        {{ column.label }}
      </div>
    </div>
    <RecycleScroller
      class="scroller"
      :items="data"
      :item-size="itemHeight"
      key-field="id"
      v-slot="{ item, index }"
    >
      <div class="table-row" :style="{ height: itemHeight + 'px' }">
        <div
          v-for="column in columns"
          :key="column.key"
          class="data-cell"
          :style="{ width: column.width ? column.width + 'px' : 'auto' }"
        >
          {{ item[column.key] }}
        </div>
      </div>
    </RecycleScroller>
  </div>
</template>

<script>
import { RecycleScroller } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';

export default {
  name: 'VirtualizedTable',
  components: {
    RecycleScroller,
  },
  props: {
    data: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
      validator: (cols) => cols.every(col => 'key' in col && 'label' in col),
    },
    itemHeight: {
      type: Number,
      default: 40, // Default height for each row
    },
  },
  methods: {
    // If you need to implement in-place editing, this is where you'd add methods
    // to update the 'data' prop (or emit an event to the parent to update it).
    // For example, a method to update a cell value:
    // updateCellValue(rowIndex, columnKey, newValue) {
    //   const updatedData = [...this.data];
    //   updatedData[rowIndex][columnKey] = newValue;
    //   this.$emit('update:data', updatedData); // Emit event to update parent's data
    // }
  },
};
</script>

<style scoped>
.virtualized-table-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* Take full height of parent */
  overflow: hidden; /* Hide scrollbars from this container, scroller handles it */
  border: 1px solid #ddd;
}

.table-header {
  display: flex;
  background-color: #f0f0f0;
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  padding-right: 17px; /* Compensate for scrollbar width */
  box-sizing: border-box;
}

.header-cell,
.data-cell {
  padding: 8px 12px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0; /* Prevent cells from shrinking */
  box-sizing: border-box;
}

.header-cell {
  border-right: 1px solid #e0e0e0;
}

.header-cell:last-child {
  border-right: none;
}

.scroller {
  flex-grow: 1; /* Allow scroller to take remaining height */
  overflow-y: auto; /* Enable vertical scrolling */
}

.table-row {
  display: flex;
  border-bottom: 1px solid #eee;
  background-color: #fff;
}

.table-row:nth-child(even) {
  background-color: #f9f9f9;
}

.data-cell {
  border-right: 1px solid #f0f0f0;
}
.data-cell:last-child {
  border-right: none;
}
</style>