# 增强版综合查询系统

## 概述

增强版综合查询系统是对原有综合查询功能的全面升级，集成了快照保存、版本控制、高级筛选等功能，提供更加现代化和高效的数据查询体验。

## 🚀 新增功能

### 1. 快照保存功能
- **一键保存**: 将当前查询结果保存为快照
- **备注信息**: 为每个快照添加描述性备注
- **快速加载**: 从快照列表中快速恢复数据状态
- **版本管理**: 支持多个快照版本的管理

### 2. 版本控制系统
- **时间线展示**: 以时间线形式展示所有快照版本
- **版本对比**: 清晰显示每个版本的创建时间和备注
- **快速切换**: 一键切换到任意历史版本
- **版本删除**: 支持删除不需要的历史版本

### 3. 集成查询面板
- **表格内查询**: 查询功能直接集成在表格组件中
- **动态表选择**: 支持动态选择不同数据表
- **多条件查询**: 支持复杂的多条件组合查询
- **查询模板**: 预设常用查询模板，快速开始

### 4. 高级数据筛选
- **多条件筛选**: 支持多个筛选条件的组合
- **逻辑运算**: 支持AND/OR逻辑关系
- **实时筛选**: 筛选结果实时更新
- **筛选状态**: 显示筛选前后的记录数量

### 5. 增强的用户界面
- **现代化设计**: 采用现代化的UI设计风格
- **响应式布局**: 支持不同屏幕尺寸的自适应
- **交互优化**: 更加直观的用户交互体验
- **状态反馈**: 清晰的操作状态和结果反馈

## 📋 功能对比

| 功能 | 原版 | 增强版 |
|------|------|--------|
| 数据展示 | Univer电子表格 | VTable高性能表格 |
| 查询界面 | 独立规则配置面板 | 集成查询面板 |
| 快照保存 | ❌ | ✅ 支持备注 |
| 版本控制 | ❌ | ✅ 时间线展示 |
| 数据筛选 | 基础筛选 | 高级多条件筛选 |
| 数据编辑 | ✅ | ✅ 增强编辑 |
| Excel导入 | ✅ | ✅ |
| Excel导出 | ✅ | ✅ 增强导出 |
| 性能 | 中等 | 高性能 |
| 用户体验 | 传统 | 现代化 |

## 🛠️ 技术架构

### 前端技术栈
- **Vue 3**: 响应式框架
- **VTable**: 高性能表格组件
- **ExcelJS**: Excel文件处理
- **现代CSS**: 响应式样式设计

### 后端API
- **FastAPI**: 高性能API框架
- **DuckDB**: 嵌入式数据库
- **快照存储**: JSON格式数据存储
- **版本管理**: 时间戳版本控制

### 数据流
```
用户操作 → 查询面板 → API请求 → 数据库查询 → 结果展示 → 快照保存
```

## 📖 使用指南

### 1. 基础查询操作

1. **选择数据表**
   - 在查询面板中选择要查询的数据表
   - 系统会自动加载对应的字段列表

2. **配置查询条件**
   - 选择字段、操作符和值
   - 支持多个条件的组合
   - 可选择AND/OR逻辑关系

3. **执行查询**
   - 点击"执行查询"按钮
   - 查询结果会实时显示在表格中

### 2. 快照管理

1. **保存快照**
   - 查询完成后点击"保存快照"
   - 输入备注信息（可选）
   - 确认保存

2. **加载快照**
   - 点击"加载快照"查看快照列表
   - 选择要加载的快照
   - 系统会恢复对应的数据状态

3. **版本历史**
   - 点击"版本历史"查看时间线
   - 可以快速切换到任意版本
   - 支持删除不需要的版本

### 3. 数据筛选

1. **添加筛选条件**
   - 在筛选面板中添加条件
   - 选择列、操作符和值
   - 支持多个条件组合

2. **应用筛选**
   - 点击"筛选"按钮应用条件
   - 表格会显示筛选后的结果
   - 状态栏显示筛选前后的记录数

3. **重置筛选**
   - 点击"重置"恢复原始数据
   - 清除所有筛选条件

### 4. 数据导出

1. **Excel导出**
   - 点击"导出Excel"按钮
   - 系统会生成包含当前数据的Excel文件
   - 支持表头样式和自动列宽

## 🔧 配置说明

### 表格配置
```javascript
{
  width: 1200,           // 表格宽度
  height: 600,           // 表格高度
  showFilter: true,      // 显示筛选面板
  showQueryPanel: true,  // 显示查询面板
  editable: true,        // 允许编辑
  enableCopyPaste: true, // 启用复制粘贴
}
```

### 查询配置
```javascript
{
  table: 'table_name',   // 数据表名
  rules: [               // 查询规则
    {
      field: 'field_name',
      operator: 'equals',
      value: 'value',
      logic: 'and'
    }
  ]
}
```

## 🚀 部署说明

### 前端部署
```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 生产构建
npm run build
```

### 后端部署
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

## 🐛 故障排除

### 常见问题

1. **快照保存失败**
   - 检查后端API是否正常运行
   - 确认数据库连接是否正常
   - 查看浏览器控制台错误信息

2. **查询无结果**
   - 检查查询条件是否正确
   - 确认数据表中是否有匹配数据
   - 验证字段名和值的格式

3. **表格显示异常**
   - 刷新页面重新加载
   - 检查数据格式是否正确
   - 确认VTable组件是否正常初始化

### 性能优化

1. **大数据量处理**
   - 使用分页查询
   - 限制查询结果数量
   - 优化查询条件

2. **快照管理**
   - 定期清理旧快照
   - 限制快照数量
   - 压缩快照数据

## 📞 技术支持

如有问题或建议，请联系开发团队或提交Issue。

---

*增强版综合查询系统 - 让数据查询更加高效便捷*
