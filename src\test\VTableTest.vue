<template>
  <div class="vtable-test">
    <h2>VTable 功能测试</h2>
    
    <!-- 测试按钮 -->
    <div class="test-buttons">
      <button @click="addTestRow" class="test-btn">添加测试行</button>
      <button @click="deleteSelectedRows" class="test-btn">删除选中行</button>
      <button @click="exportToExcel" class="test-btn">导出Excel</button>
      <button @click="exportToCSV" class="test-btn">导出CSV</button>
      <button @click="resetData" class="test-btn">重置数据</button>
    </div>
    
    <!-- VTable组件 -->
    <VTableComponent
      ref="vtableRef"
      :data="testData"
      :width="800"
      :height="400"
      :show-filter="true"
      :editable="true"
      :enable-copy-paste="true"
      :auto-width="true"
      @data-change="handleDataChange"
      @cell-edit="handleCellEdit"
    />
    
    <!-- 数据信息 -->
    <div class="data-info">
      <h3>当前数据信息</h3>
      <p>总行数: {{ testData.length - 1 }}</p>
      <p>总列数: {{ testData.length > 0 ? testData[0].length : 0 }}</p>
      <p>最后更新: {{ lastUpdate }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 测试数据
const testData = ref([
  ['姓名', '年龄', '部门', '工资', '入职日期', '备注'],
  ['张三', 28, '技术部', 15000, '2023-01-15', '高级工程师'],
  ['李四', 32, '销售部', 12000, '2022-06-20', '销售经理'],
  ['王五', 25, '人事部', 8000, '2023-03-10', '人事专员'],
  ['赵六', 30, '财务部', 10000, '2022-12-01', '会计师'],
  ['钱七', 27, '技术部', 13000, '2023-02-28', '前端工程师']
])

const vtableRef = ref(null)
const lastUpdate = ref(new Date().toLocaleString())

// 添加测试行
const addTestRow = () => {
  if (vtableRef.value) {
    const newRowData = [
      `测试用户${Date.now()}`,
      Math.floor(Math.random() * 20) + 25,
      '测试部门',
      Math.floor(Math.random() * 10000) + 8000,
      new Date().toISOString().slice(0, 10),
      '测试数据'
    ]
    
    vtableRef.value.addRowData(newRowData)
    lastUpdate.value = new Date().toLocaleString()
  }
}

// 删除选中行
const deleteSelectedRows = () => {
  if (vtableRef.value) {
    vtableRef.value.deleteRows()
    lastUpdate.value = new Date().toLocaleString()
  }
}

// 导出Excel
const exportToExcel = () => {
  if (vtableRef.value) {
    vtableRef.value.exportExcel()
  }
}

// 导出CSV
const exportToCSV = () => {
  if (vtableRef.value) {
    vtableRef.value.exportCSV()
  }
}

// 重置数据
const resetData = () => {
  testData.value = [
    ['姓名', '年龄', '部门', '工资', '入职日期', '备注'],
    ['张三', 28, '技术部', 15000, '2023-01-15', '高级工程师'],
    ['李四', 32, '销售部', 12000, '2022-06-20', '销售经理'],
    ['王五', 25, '人事部', 8000, '2023-03-10', '人事专员']
  ]
  lastUpdate.value = new Date().toLocaleString()
}

// 处理数据变化
const handleDataChange = (newData) => {
  console.log('数据变化:', newData)
  lastUpdate.value = new Date().toLocaleString()
}

// 处理单元格编辑
const handleCellEdit = (editInfo) => {
  console.log('单元格编辑:', editInfo)
  lastUpdate.value = new Date().toLocaleString()
}
</script>

<style scoped>
.vtable-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #409eff;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.test-btn:hover {
  background-color: #66b1ff;
  transform: translateY(-1px);
}

.test-btn:active {
  transform: translateY(0);
}

.data-info {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.data-info h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.data-info p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}
</style>
